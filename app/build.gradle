plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'org.jetbrains.kotlin.kapt'
    id 'com.google.dagger.hilt.android'
    id 'org.jetbrains.kotlin.plugin.serialization'
    id 'com.apollographql.apollo3'
    id 'com.google.gms.google-services'
    id("com.google.firebase.crashlytics")
    id 'com.google.firebase.firebase-perf'

    id 'io.sentry.android.gradle' version '3.14.0'
    id "com.google.protobuf" version "0.9.1"
}

android {
    signingConfigs {
        release {
            storeFile file('keystore/android_keystore.jks')
            storePassword System.getenv("SIGNING_STORE_PASSWORD")
            keyAlias System.getenv("SIGNING_KEY_ALIAS")
            keyPassword System.getenv("SIGNING_KEY_PASSWORD")
        }
    }
    namespace 'com.swiftsku.swiftpos'
    compileSdk 34
    ndkVersion = "25.2.9519653"
    defaultConfig {
        applicationId "com.swiftsku.swiftpos"
        minSdk 28
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            buildConfigField "String", "SG_URL", STAGING_SYNC_GATEWAY_URL
            buildConfigField "String", "LOYALTY_URL", STAGING_LOYALTY_URL
            buildConfigField "String", "REPORT_URL", REPORT_URL
            buildConfigField "String", "PAYMENT_URL", STAGING_PAYMENT_URL
            buildConfigField "String", "PAYMENT_WEBSOCKET_URL", PAYMENT_WEBSOCKET_URL
            buildConfigField "String", "TEST_POS_ID", TEST_POS_ID
            buildConfigField "String", "TEST_SG_USERNAME", TEST_SG_USERNAME
            buildConfigField "String", "TEST_SG_PASSWORD", TEST_SG_PASSWORD
            buildConfigField "String", "USER_DATA_STORE_FILE_NAME", USER_DATA_STORE_FILE_NAME
            buildConfigField "Long", "PAYMENT_TIMEOUT", PAYMENT_TIMEOUT
            resValue "string", "SENTRY_DSN", '""'
            buildConfigField "boolean", "ENABLE_EMULATOR", 'true'
        }
        staging {
            applicationIdSuffix ".staging"
            buildConfigField "String", "SG_URL", STAGING_SYNC_GATEWAY_URL
            buildConfigField "String", "LOYALTY_URL", STAGING_LOYALTY_URL
            buildConfigField "String", "REPORT_URL", REPORT_URL
            buildConfigField "String", "PAYMENT_WEBSOCKET_URL", PAYMENT_WEBSOCKET_URL

            buildConfigField "String", "PAYMENT_URL", STAGING_PAYMENT_URL
            buildConfigField "String", "TEST_POS_ID", TEST_POS_ID
            buildConfigField "String", "TEST_SG_USERNAME", TEST_SG_USERNAME
            buildConfigField "String", "TEST_SG_PASSWORD", TEST_SG_PASSWORD
            buildConfigField "String", "USER_DATA_STORE_FILE_NAME", USER_DATA_STORE_FILE_NAME

            buildConfigField "Long", "PAYMENT_TIMEOUT", PAYMENT_TIMEOUT
            buildConfigField "boolean", "ENABLE_EMULATOR", 'false'
            resValue "string", "SENTRY_DSN", SENTRY_DSN


            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel 'none'
            }
            signingConfig signingConfigs.debug
        }
        release {
            buildConfigField "String", "SG_URL", System.getenv("SG_WS_URL") ?: '"wss://swpossync.swiftsku.com/main"'
            buildConfigField "String", "LOYALTY_URL", System.getenv("LOYALTY_URL") ?: '"https://posloyalty.swiftsku.com/gql"'
            buildConfigField "String", "PAYMENT_URL", System.getenv("PAYMENTURL") ?: '"https://swiftpayments.swiftsku.com/gql"'
            buildConfigField "String", "REPORT_URL", '"https://swposapi.swiftsku.com/gql"'
            buildConfigField "String", "PAYMENT_WEBSOCKET_URL", '"wss://swiftpayments.swiftsku.com/wss/payments"'

            buildConfigField "String", "USER_DATA_STORE_FILE_NAME", USER_DATA_STORE_FILE_NAME

            buildConfigField "String", "TEST_POS_ID", '"INVALID_POS_ID"'
            buildConfigField "String", "TEST_SG_USERNAME", '"INVALID_USER"'
            buildConfigField "String", "TEST_SG_PASSWORD", '"INVALID_PASSWORD"'
            buildConfigField "Long", "PAYMENT_TIMEOUT", PAYMENT_TIMEOUT
            resValue "string", "SENTRY_DSN", SENTRY_DSN

            buildConfigField "boolean", "ENABLE_EMULATOR", 'false'

            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            ndk {
                debugSymbolLevel 'none'
            }
            firebaseCrashlytics {
                // Enable processing and uploading of native symbols to Firebase servers.
                // By default, this is disabled to improve build speeds.
                // This flag must be enabled to see properly-symbolicated native
                // stack traces in the Crashlytics dashboard.
                nativeSymbolUploadEnabled true
                strippedNativeLibsDir 'build/intermediates/stripped_native_libs/release/out/lib'
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }

            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.7'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar","*.aar"])

    // Core
    implementation 'androidx.core:core-ktx:1.12.0'

    implementation "com.lambdaworks:scrypt:1.4.0"

    //Firebase
    implementation platform('com.google.firebase:firebase-bom:32.8.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation("com.google.firebase:firebase-crashlytics-ndk")
    implementation("com.google.firebase:firebase-perf-ktx")

    //Logger
    implementation 'com.orhanobut:logger:2.2.0'

    // Printer / Cash Drawer
    implementation 'com.sunmi:printerx:1.0.11'
    // for legacy support of 3.3.32
    implementation 'com.sunmi:printerlibrary:1.0.19'

    // JSON serialization
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1"

    // Database
    implementation 'com.couchbase.lite:couchbase-lite-android-ee-ktx:3.1.0'

    // PostHog
    implementation("com.posthog:posthog-android:3.+")

    // DataStore
    implementation "androidx.datastore:datastore:1.0.0"
    implementation "com.google.protobuf:protobuf-javalite:3.21.11"

    // WorkManager
    implementation "androidx.work:work-runtime-ktx:$work_version"

    // DI
    implementation "androidx.hilt:hilt-navigation-compose:$hilt_version"
    implementation("com.google.dagger:hilt-android:2.51.1")
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.1'
    implementation 'androidx.compose.material3:material3:1.1.2'
    implementation 'androidx.datastore:datastore-core-android:1.1.0-alpha06'

    // FDC Core/Dispenser
    implementation files('libs/fdc-core-release.aar')
    // https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind
    implementation("com.fasterxml.jackson.core:jackson-databind:2.17.1") {
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-annotations'
    }


    testImplementation 'org.junit.jupiter:junit-jupiter:5.8.1'
    kapt("com.google.dagger:hilt-android-compiler:2.51.1")
    implementation("androidx.hilt:hilt-work:1.0.0")
    kapt("androidx.hilt:hilt-compiler:1.0.0")

    //Image
    implementation "io.coil-kt:coil-svg:$coil"
    implementation "io.coil-kt:coil-compose:$coil"

    // Navigation
    implementation "androidx.navigation:navigation-compose:$nav_version"

    // Coroutine
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutine_version"

    // DataStore
    implementation "androidx.datastore:datastore:$data_store"
    implementation "androidx.datastore:datastore-rxjava3:$data_store"

    // Apollo
    implementation "com.apollographql.apollo3:apollo-runtime:3.8.2"

    // Compose
    implementation 'androidx.activity:activity-compose:1.7.2'
    implementation "androidx.compose.ui:ui:$compose_ui_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_ui_version"
    implementation "androidx.compose.material:material:$compose_ui_version"
    implementation "androidx.compose.material:material-icons-extended:$compose_ui_version"
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_ui_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_ui_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_ui_version"

    // LifeCycle
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'

    // Reordering Grid Items
    implementation("org.burnoutcrew.composereorderable:reorderable:0.9.6")

    //Test
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    // Templating for Receipt Data
    // Need to version 4.2.1 because version 4.3.0 requires higher level of Java
    //noinspection GradleDependency
    implementation 'com.github.jknack:handlebars:4.2.1'
    implementation 'com.caverock:androidsvg-aar:1.4'

    // SLF4J API
    implementation 'org.slf4j:slf4j-api:1.7.36'

// Logging binding
    runtimeOnly 'ch.qos.logback:logback-classic:1.2.11'

    // zxing
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.21.7"
    }

    // Generates the java Protobuf-lite code for the Protobufs in this project. See
    // https://github.com/google/protobuf-gradle-plugin#customizing-protobuf-compilation
    // for more information.
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {
                    option 'lite'
                }
            }
        }
    }
}

apollo {
    service("payment") {
        packageName.set("com.swiftsku.swiftpos.payment")
    }
}

kapt {
    correctErrorTypes = true
}


sentry {
    org = "swiftsku"
    projectName = "swiftpos-native"

    // this will upload your source code to Sentry to show it as part of the stack traces
    // disable if you don't want to expose your sources
    includeSourceContext = true
}
