package com.swiftsku.swiftpos

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.hilt.navigation.compose.hiltViewModel
import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.swiftpos.lifecycle.observer.printer.PrinterObserver
import com.swiftsku.swiftpos.ui.activity.base.ActivityWithSecondaryDisplay
import com.swiftsku.swiftpos.ui.activity.base.AppRoot
import com.swiftsku.swiftpos.ui.activity.base.secondaryDisplay
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


@AndroidEntryPoint
class MainActivity : ActivityWithSecondaryDisplay() {

    @Inject
    lateinit var printerObserver: PrinterObserver

    @Inject
    lateinit var socketManager: WebSocketManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        printerObserver.registerLifecycle(lifecycle = lifecycle)

        setContent {
            AppRoot(
                configVM = hiltViewModel(),
                dashboardVM = hiltViewModel(),
                loginVM = hiltViewModel(),
                fdcVM = hiltViewModel(),
                secondaryDisplay = secondaryDisplay,
                fdcSocketManager = socketManager,
                recallVM = hiltViewModel(),
                transactionHistoryVM = hiltViewModel(),
                transactionHistoryDetailVM = hiltViewModel(),
                reportVM = hiltViewModel(),
                creditsVM = hiltViewModel()
            )
        }
    }
}



