package com.swiftsku.swiftpos.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.ExperimentalMultiProcessDataStore
import androidx.datastore.core.MultiProcessDataStoreFactory
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.data.local.datastore.user.User.UserProto
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.UserProtoSerializer
import com.swiftsku.swiftpos.data.local.datastore.user.proto.UserProtoDataSource
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import java.io.File
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object AppDataStoreModule {

    @OptIn(ExperimentalMultiProcessDataStore::class)
    @Singleton
    @Provides
    fun provideUserDataStoreProto(@ApplicationContext context: Context): DataStore<UserProto> =
        MultiProcessDataStoreFactory.create(
            serializer = UserProtoSerializer(),
            produceFile = {
                File("${context.cacheDir.path}/${BuildConfig.USER_DATA_STORE_FILE_NAME}")
            }
        )

    @Singleton
    @Provides
    fun provideUserDataStore(
        @IODispatcher dispatcher: CoroutineDispatcher,
        userProtoDataStore: DataStore<UserProto>
    ): UserDataSource =
        UserProtoDataSource(dispatcher = dispatcher, dataStore = userProtoDataStore)


}
