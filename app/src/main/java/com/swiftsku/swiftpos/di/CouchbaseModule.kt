package com.swiftsku.swiftpos.di

import com.couchbase.lite.Collection
import com.couchbase.lite.Database
import com.couchbase.lite.DatabaseConfiguration
import com.couchbase.lite.DatabaseConfigurationFactory
import com.couchbase.lite.ReplicatorConfiguration
import com.couchbase.lite.ReplicatorConfigurationFactory
import com.couchbase.lite.ReplicatorType
import com.couchbase.lite.URLEndpoint
import com.couchbase.lite.newConfig
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.data.couchbase.Couchbase
import com.swiftsku.swiftpos.di.qualifiers.AccountLedgersCollection
import com.swiftsku.swiftpos.di.qualifiers.CreditAccountsCollection
import com.swiftsku.swiftpos.di.qualifiers.EventCollection
import com.swiftsku.swiftpos.di.qualifiers.GUPCCollection
import com.swiftsku.swiftpos.di.qualifiers.GUPCReportCollection
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PaymentRecordsCollection
import com.swiftsku.swiftpos.di.qualifiers.PriceBookCollection
import com.swiftsku.swiftpos.di.qualifiers.PromotionsCollection
import com.swiftsku.swiftpos.di.qualifiers.RefDataCollection
import com.swiftsku.swiftpos.di.qualifiers.ReportCollection
import com.swiftsku.swiftpos.di.qualifiers.StoreCollection
import com.swiftsku.swiftpos.di.qualifiers.TransactionCollection
import com.swiftsku.swiftpos.services.replicator.CouchbaseReplicatorService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import java.net.URI
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CouchbaseModule {

    @Provides
    fun provideDatabaseConfiguration(): DatabaseConfiguration =
        DatabaseConfigurationFactory.newConfig()

    @Singleton
    @Provides
    fun provideCouchbaseDatabase(
        config: DatabaseConfiguration
    ): Database = runBlocking(Dispatchers.IO) {
        Database("swift-pos-db", config)
    }

    @Singleton
    @Provides
    fun provideCouchbase(database: Database): Couchbase = runBlocking(Dispatchers.IO) {
        Couchbase(database = database)
    }


    @Provides
    fun provideReplicatorConfiguration(
        @PriceBookCollection priceBookCollection: Collection,
        @GUPCCollection gUpcCollection: Collection,
        @RefDataCollection refDataCollection: Collection,
        @TransactionCollection transactionCollection: Collection,
        @PromotionsCollection promotionsCollection: Collection,
        @StoreCollection storeCollection: Collection,
        @ReportCollection reportCollection: Collection,
        @EventCollection eventCollection: Collection,
        @GUPCReportCollection gUPCReportCollection: Collection,
        @PaymentRecordsCollection paymentRecordsCollection: Collection
    ): ReplicatorConfiguration = ReplicatorConfigurationFactory.newConfig(
        target = URLEndpoint(URI(BuildConfig.SG_URL)),
        collections = mapOf(
            setOf(
                priceBookCollection,
                gUpcCollection,
                refDataCollection,
                transactionCollection,
                promotionsCollection,
                storeCollection,
                reportCollection,
                eventCollection,
                gUPCReportCollection,
                paymentRecordsCollection
            ) to null
        ),
        type = ReplicatorType.PUSH_AND_PULL,
        continuous = true,
        maxAttempts = 0
    )


    @Provides
    @Singleton
    fun providesReplicatorService(
        replicatorConfiguration: ReplicatorConfiguration,
        @IODispatcher dispatcher: CoroutineDispatcher,
        @TransactionCollection transactionCollection: Collection
    ): CouchbaseReplicatorService =
        CouchbaseReplicatorService(
            replicatorConfiguration,
            dispatcher,
            transactionCollection
        )


    @PriceBookCollection
    @Provides
    fun providePriceBookCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "pricebooks", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("pricebooks", "cloud")


    @GUPCCollection
    @Provides
    fun provideGUPCCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "gupc", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("gupc", "cloud")


    @RefDataCollection
    @Provides
    fun provideRefDataCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "refdata", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("refdata", "cloud")


    @TransactionCollection
    @Provides
    fun provideTransactionCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "txn", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("txn", "cloud")


    @PromotionsCollection
    @Provides
    fun providePromotionCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "promotions", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("promotions", "cloud")


    @StoreCollection
    @Provides
    fun provideStoreCollection(
        couchbase: Couchbase
    ): Collection =
        couchbase.getCollection(
            "storeConfig", "cloud"
        )?.takeIf { true } ?: couchbase.createCollection("storeConfig", "cloud")


    @EventCollection
    @Provides
    fun provideEventCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "events", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("events", "cloud")

    @ReportCollection
    @Provides
    fun provideReportCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "reports", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("reports", "cloud")

    @GUPCReportCollection
    @Provides
    fun provideGUPCReportCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "gupcCorrection", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("gupcCorrection", "cloud")

    @PaymentRecordsCollection
    @Provides
    fun providePaymentRecordsCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "payment_records", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("payment_records", "cloud")

    @CreditAccountsCollection
    @Provides
    fun provideCreditAccountsCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "creditAccounts", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("creditAccounts", "cloud")

    @AccountLedgersCollection
    @Provides
    fun provideAccountLedgersCollection(
        couchbase: Couchbase
    ): Collection = couchbase.getCollection(
        "accountLedgers", "cloud"
    )?.takeIf { true } ?: couchbase.createCollection("accountLedgers", "cloud")
}