package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import com.github.jknack.handlebars.Handlebars
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.modules.printer.BitmapGenerator
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext


class TransactionTemplatePrinter(
    private val transactionTemplate: TransactionTemplate,
    @IODispatcher private val coroutineDispatcher: CoroutineDispatcher,
    private val handlebars: Handlebars = Handlebars(),
) : BitmapGenerator {


    override suspend fun generateBitmap(): Bitmap {
        return withContext(coroutineDispatcher) {

            // Draw store details
            val storeBitmap = transactionTemplate.storeTemplate.drawBitmap(handlebars)

            // Draw txn info
            val txnInfoBitmap = transactionTemplate.txnInfoTemplate?.drawBitmap(handlebars)

            // Draw cart
            val cartBitmap = transactionTemplate.cartTemplate?.drawBitmap(handlebars)

            // Draw payout data
            val payoutBitmap = transactionTemplate.payoutTemplate?.drawBitmap(handlebars)

            val cashAdjustmentBitmap =
                transactionTemplate.cashAdjustmentTemplate?.drawBitmap(handlebars)

            // Draw transaction summary
            val txnSummaryBitmap =
                transactionTemplate.transactionSummaryTemplate?.drawBitmap(handlebars)

            // Draw card details
            val cardDetailBitmap = transactionTemplate.cardDetail?.drawBitmap(handlebars)

            // Draw loyalty
            val loyaltyBitmap = transactionTemplate.loyaltyTemplate?.drawBitmap(handlebars)

            // Draw merchant copy
            var merchantCopyBitmap: Bitmap? = null
            if (transactionTemplate.printFooter) {
                merchantCopyBitmap = transactionTemplate.drawMerchantCopyBitmap()
            }

            // Draw EBT details
            val ebtBitmap = transactionTemplate.ebtTemplate?.drawBitmap(handlebars)

            val barcodeBitmap = transactionTemplate.barcodeTemplate?.drawBitmap()

            // Combine all bitmaps into one
            val combinedWidth = storeBitmap.width
            val combinedHeight =
                storeBitmap.height +
                        (txnInfoBitmap?.height ?: 0) +
                        (cartBitmap?.height ?: 0) +
                        (payoutBitmap?.height ?: 0) +
                        (cashAdjustmentBitmap?.height ?: 0) +
                        (txnSummaryBitmap?.height ?: 0) +
                        (cardDetailBitmap?.height ?: 0) +
                        (loyaltyBitmap?.height ?: 0) +
                        (ebtBitmap?.height ?: 0) +
                        (merchantCopyBitmap?.height ?: 0) +
                        (barcodeBitmap?.height ?: 0) + 10

            val combinedBitmap =
                Bitmap.createBitmap(combinedWidth, combinedHeight, Bitmap.Config.RGB_565)
            val combinedCanvas = Canvas(combinedBitmap)
            combinedCanvas.drawColor(Color.WHITE)

            var top = 0f
            combinedCanvas.drawBitmap(storeBitmap, 0f, top, null)
            top += storeBitmap.height.toFloat()

            txnInfoBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            cartBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            payoutBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            cashAdjustmentBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            txnSummaryBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            cardDetailBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            loyaltyBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            ebtBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            merchantCopyBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            barcodeBitmap?.let {
                combinedCanvas.drawBitmap(it, 0f, top, null)
                top += it.height.toFloat()
            }

            combinedBitmap
        }
    }

}