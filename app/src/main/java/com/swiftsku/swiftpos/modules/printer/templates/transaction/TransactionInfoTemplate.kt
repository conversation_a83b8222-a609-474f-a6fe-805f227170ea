package com.swiftsku.swiftpos.modules.printer.templates.transaction

import android.graphics.Bitmap
import android.graphics.Canvas
import com.caverock.androidsvg.SVG
import com.github.jknack.handlebars.Handlebars

val txnInfoSvgTemplate = """
<svg width="576" height="80" viewBox="0 0 576 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#fff"/>
  <text fill="#000" xml:space="preserve" style="white-space:pre" font-family="Roboto" font-size="28" font-weight="600" letter-spacing="0em" text-anchor="middle"><tspan x="50%" y="50">{{txnType}}</tspan></text>
</svg>
""".trimIndent();


data class TxnInfoTemplate(
    val txnType: String
)

fun TxnInfoTemplate.map() = mapOf(
    "txnType" to txnType
)

fun TxnInfoTemplate.drawBitmap(handlebars: Handlebars): Bitmap {

    val data = map()

    val txnInfoTemplate = handlebars.compileInline(txnInfoSvgTemplate)
    val txnInfoTemplateFilledData = txnInfoTemplate.apply(data)
    val txnInfoSvg = SVG.getFromString(txnInfoTemplateFilledData)
    txnInfoSvg.renderDPI = 300f
    val txnInfoPicture = txnInfoSvg.renderToPicture()
    val txnInfoBitmap = Bitmap.createBitmap(
        txnInfoPicture.width,
        txnInfoPicture.height,
        Bitmap.Config.RGB_565
    )
    val txnInfoCanvas = Canvas(txnInfoBitmap)
    txnInfoCanvas.drawPicture(txnInfoPicture)

    return txnInfoBitmap
}