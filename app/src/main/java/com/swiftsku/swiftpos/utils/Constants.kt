package com.swiftsku.swiftpos.utils

import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.extension.years


const val ENABLE_EMULATOR = BuildConfig.ENABLE_EMULATOR
const val MAX_ATTEMPTS_REACHED = "MAX_ATTEMPTS_REACHED"
const val INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS"


val months = listOf(
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
)
val years = years()


object DocumentFieldKeys {
    const val IS_INTEGRATED_FIELD = "is_integrated"
    const val IS_CASH_INTEGRATED_FIELD = "is_cash_integrated"
    const val TXN_STATUS = "txnStatus"
}

const val MAX_WS_CONNECTION_RETRIES = 10

const val FUEL_TXN_STALE_PERIOD_DAYS = 60L
const val UNSYNCED_TXN = "unsynced_txn"

const val FUEL_CASH_MODE = 1
const val FUEL_CARD_MODE = 2

const val PENDING_TXN_QUERY_DELAY = 60_000L

const val TXN_LABEL_LENGTH = 15
const val VENDOR_NAME_LENGTH = 10

const val ACCOUNT_NAME_LENGTH = 15