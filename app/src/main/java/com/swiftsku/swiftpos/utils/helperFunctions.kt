package com.swiftsku.swiftpos.utils

import android.util.Log
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter

fun isValidDate(date: String, minDate: LocalDate? = null, maxDate: LocalDate? = null): Boolean {
    val dateFormatter = DateTimeFormatter.ofPattern("MMddyyyy")
    return try {
        val parsedDate = LocalDate.parse(date, dateFormatter)

        if (minDate != null) {
            return parsedDate >= minDate
        }

        if (maxDate != null) {
            return parsedDate <= maxDate
        }

        true // Date is valid
    } catch (e: Exception) {
        false // Date is invalid
    }
}

fun String.parseDate(dateFormat: String = "MMddyyyy"): LocalDate? {
    return try {
        val dateFormatter = DateTimeFormatter.ofPattern(dateFormat)
        LocalDate.parse(this, dateFormatter)
    } catch (e: Exception) {
        null
    }
}

fun getAgeInYearsAndMonths(dob: LocalDate): String {
    val period = Period.between(dob, LocalDate.now())
    return "${period.years} years${if (period.months > 0) " and ${period.months} months" else ""}"
}

fun formatAsUSPhoneNumber(input: String): String {
    val digits = input.filter { it.isDigit() }.takeLast(10) // get last 10 digits
    return if (digits.length == 10) {
        "(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}"
    } else {
        input // return as-is if not exactly 10 digits
    }
}