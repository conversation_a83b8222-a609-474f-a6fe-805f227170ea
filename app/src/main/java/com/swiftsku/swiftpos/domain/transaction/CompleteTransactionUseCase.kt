package com.swiftsku.swiftpos.domain.transaction

import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.FuelMeta
import com.swiftsku.swiftpos.data.model.PumpMeta
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TxnDiscount
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payout.CreateLotteryPayoutUseCase
import com.swiftsku.swiftpos.domain.payout.dto.PayoutInput
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.domain.printer.dto.PrintTxnReceiptInput
import com.swiftsku.swiftpos.domain.transaction.dto.CompleteTxnInput
import com.swiftsku.swiftpos.domain.transaction.dto.CompleteTxnOutput
import com.swiftsku.swiftpos.domain.transaction.dto.accountInfo
import com.swiftsku.swiftpos.domain.transaction.dto.transformedTxnItems
import com.swiftsku.swiftpos.domain.transaction.dto.txnStatus
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.ui.dashboard.main.state.change
import java.util.Date
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

class CompleteTransactionUseCase
@Inject
constructor(
        private val transactionRepository: TransactionRepository,
        @IODispatcher private val dispatcher: CoroutineDispatcher,
        private val createLotteryPayout: CreateLotteryPayoutUseCase,
        private val printTransaction: PrintTransactionUseCase,
) {
    suspend operator fun invoke(input: CompleteTxnInput): CompleteTxnOutput =
            withContext(dispatcher) {
                val transaction = input.transaction
                val txnPayment = input.txnPayment
                val transactionSummary = input.transactionSummary
                val loyaltyId = input.loyaltyId
                val dob = input.dob
                val txnItems = input.transformedTxnItems
                val coupons = input.coupons
                val storeConfig = input.storeConfig
                val cashierId = input.cashierId
                val cardInfo = input.cardInfo
                val ebtInfo = input.ebtInfo

                val lotteryPayouts = input.payout.lotteryPayouts

                val txnItemsPresent = txnItems.any { it.status == TransactionItemStatus.Normal }
                if (lotteryPayouts.isNotEmpty()) {
                    createLotteryPayout(
                        PayoutInput(
                            lotteryPayouts = lotteryPayouts,
                            cashierId = cashierId,
                            fromSale = txnItemsPresent,
                            txnStartTime = transaction.txnStartTime,
                            storeConfig = storeConfig
                        )
                    )
                    // In case user is trying to create only lottery payouts, don't create a Sale
                    // Transaction with 0 items
                    if (!txnItemsPresent) {
                        return@withContext CompleteTxnOutput(true)
                    }
                }

                val coupon = coupons.sumOfFloats { it.amount }

                val pumpMetas =
                        txnItems.filterIsInstance<TransactionItemWithFuel>().map {
                            PumpMeta(it.preFuel.deviceId)
                        }

                val fuelMeta = if (pumpMetas.isNotEmpty()) FuelMeta(pumpMetas) else null

                val saveTransaction =
                        transactionRepository.saveTransaction(
                                SaleTransaction(
                                        txnEndTime = Date(),
                                        txnStartTime = transaction.txnStartTime,
                                        txnTotalGrandAmount =
                                                transactionSummary.transactionTotalGrandAmount,
                                        txnTotalGrossAmount =
                                                transactionSummary.transactionTotalGrossAmount,
                                        txnTotalNetAmount =
                                                transactionSummary.transactionTotalNetAmount,
                                        txnTotalTaxNetAmount =
                                                transactionSummary.transactionTotalTaxNetAmount,
                                        dob = dob,
                                        cashierId = transaction.cashierId,
                                        txnStatus = input.txnStatus,
                                        statusHistory = HashMap(transaction.statusHistory.orEmpty()).apply {
                                            put(Date().epochInSeconds(), input.txnStatus)
                                        },
                                        txnItems = txnItems,
                                        txnId = transaction.txnId,
                                        txnPayment = txnPayment,
                                        txnDiscount = TxnDiscount(coupons),
                                        lotteryPayouts = lotteryPayouts,
                                        accountInfo = input.accountInfo,
                                        cardInfo = cardInfo,
                                        fuelMeta = fuelMeta,
                                        paymentRecord = input.paymentRecord,
                                        ebtInfo = ebtInfo,
                                        appliedFees = input.appliedFees
                                )
                        )

                val completeTxnOutput = CompleteTxnOutput(txnSaved = saveTransaction)

                val cartItems = txnItems.filter { it.status == TransactionItemStatus.Normal }

                var total = transactionSummary.transactionTotalGrandAmount - coupon
                if (total < 0) {
                    total = 0f
                }

                // print receipt
                val printCardReceipt = storeConfig.receiptInfo.printCardReceipt
                val printCashReceipt = storeConfig.receiptInfo.printCashReceipt

                if (printCashReceipt || (printCardReceipt && cardInfo != null)) {
                    printTransaction(
                            PrintTxnReceiptInput(
                                    storeConfig = storeConfig,
                                    txnStartTime = transaction.txnStartTime,
                                    txnId = transaction.txnId,
                                    cartItems = cartItems,
                                    coupon = coupon,
                                    subTotal = transactionSummary.transactionTotalNetAmount,
                                    tax = transactionSummary.transactionTotalTaxNetAmount,
                                    total = total,
                                    loyaltyAccount = loyaltyId,
                                    cardInfo = cardInfo,
                                    ebtInfo = ebtInfo,
                                    cardPayment = txnPayment[TxnPaymentType.Card],
                                    cashPayment = txnPayment[TxnPaymentType.Cash],
                                    ebtPayment = txnPayment[TxnPaymentType.EBT],
                                    chequePayment = txnPayment[TxnPaymentType.Cheque],
                                    change = transactionSummary.change(),
                                    appliedFees = input.appliedFees,
                                    lotteryPayout = null,
                                    lotteryPayouts = lotteryPayouts
                            )
                    )
                }

                completeTxnOutput
            }
}
