package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.ClearFuelSaleRequestType
import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.FreeFuelPointRequestType
import com.fdc.core.types.FuelSalePOSdataType
import com.fdc.core.types.FuelSaleRequestDeviceClassType
import com.fdc.core.types.FuelSalesTrxDetailsDeviceClassType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.POSdataBaseType
import com.fdc.core.types.State
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.ClearFuelSaleRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFuelSaleTransactionsMessageUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelSaleTrx
import com.swiftsku.swiftpos.data.model.FuelSaleTrxState
import com.swiftsku.swiftpos.data.model.POSTransData
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.deletedFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.parsePosDataFromFdc
import com.swiftsku.swiftpos.ui.dashboard.main.state.selectFpLock
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.util.Date
import java.util.UUID
import javax.inject.Inject

class SetupFuelSaleTransactionsListenerUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val listenForFuelSaleTransactionsMessageUseCase: ListenForFuelSaleTransactionsMessageUseCase,
    private val clearFuelSale: ClearFuelSaleRequestUseCase,
    private val freeFP: FreeFuelPointUseCase,
    private val updateFSTransactionStatus: UpdateFuelSaleTransactionStatusUseCase
) {

    private suspend fun freePump(
        deviceId: Int,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) =
        withContext(dispatcher) {

            val pumpRequestId = generatePumpRequestId(deviceId)

            launch {
                freeFP.freeFPResponse.collectLatest { response ->
                    if (response.isForCurrentDevice(
                            workstationId = fdcConfig.workstationID,
                            requestID = pumpRequestId
                        ) && response.overallResult == OverallResult.SUCCESS
                    ) {
                        stateFlow.update { state ->
                            val afterDeleting = state.deletedFpLock(deviceId)
                            afterDeleting.copy(linkedTxnId = if (afterDeleting.fpLock.isEmpty()) null else afterDeleting.linkedTxnId)
                        }
                    }
                }
            }

            launch {
                freeFP(
                    FreeFuelPointRequestType().apply {
                        applicationSender = fdcConfig.applicationSender
                        workstationID = fdcConfig.workstationID
                        requestID = pumpRequestId
                        poSdata = POSdataBaseType().apply {
                            posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                            deviceClass = DeviceClassType().apply {
                                type = Type.FP
                                deviceID = deviceId
                            }
                        }
                    }
                )
            }
        }

    private suspend fun clearFuelSale(
        device: FuelSalesTrxDetailsDeviceClassType,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {

        val requestId = generatePumpRequestId(device.pumpNo)

        launch {
            clearFuelSale.changeFuelPriceResponse.collectLatest { clear ->
                if (clear.overallResult == OverallResult.SUCCESS
                    && clear.isForCurrentDevice(
                        workstationId = fdcConfig.workstationID,
                        requestID = requestId
                    )
                ) {
                    freePump(device.deviceID, stateFlow, fdcConfig)
                }
            }
        }
        launch {
            clearFuelSale.invoke(ClearFuelSaleRequestType().apply {
                applicationSender =
                    fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = requestId
                poSdata = FuelSalePOSdataType().apply {
                    posTimeStamp =
                        LocalDateTime.now().toPOSTimeStamp()
                    deviceClass =
                        FuelSaleRequestDeviceClassType().apply {
                            deviceID = device.deviceID
                            type = device.type
                            transactionSeqNo =
                                device.transactionSeqNo
                        }
                }
            })
        }
    }

    suspend operator fun invoke(
        stateFlow: MutableStateFlow<FDCState>,
        storeConfig: StoreConfig,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        listenForFuelSaleTransactionsMessageUseCase
            .message
            .collectLatest { latestTrx ->
                stateFlow.update { state ->
                    state.copy(dispensers = state.dispensers.map { dispenserState ->
                        dispenserState.copy(fuelPump = dispenserState.fuelPump.map fpMap@{ fuelPumpState ->
                            val device = latestTrx.fdCdata.deviceClass
                            if (device.authorisationApplicationSender == fdcConfig.applicationSender
                                && device.deviceID == fuelPumpState.deviceId
                            ) {
                                val posTxnData = parsePosDataFromFdc(
                                    storeConfig.storeCode,
                                    storeConfig.posNumber,
                                    device.posTransData.posTransactionData
                                )
                                val fpLock = state.selectFpLock(
                                    pumpNo = device.pumpNo,
                                    deviceId = device.deviceID
                                )
                                if (device.state == State.PAYABLE) {
                                    val fuelSaleTrx = FuelSaleTrx(
                                        lockedAmount = (fpLock?.lockedAmount ?: 0.0).to2Decimal(),
                                        lockedVolume = fpLock?.lockedVolume ?: 0.0,
                                        pumpNo = device.pumpNo,
                                        deviceId = device.deviceID,
                                        posTransData = POSTransData(
                                            tranType = device?.posTransData?.tranType,
                                            posTransactionData = device?.posTransData?.posTransactionData,
                                            epsStan = device?.posTransData?.epsstan?.toDouble(),
                                            merchandiseTrxAmount = device?.posTransData?.merchandiseTrxAmount?.toDouble()
                                        ),
                                        volume = device?.volume?.toDouble(),
                                        amount = device?.amount?.toDouble()?.to2Decimal(),
                                        volume1 = device.volumeProduct1?.toDouble(),
                                        volume2 = device.volumeProduct2?.toDouble(),
                                        product1 = device.productNo1,
                                        product2 = device.productNo2,
                                        unitPrice = device?.unitPrice?.toDouble(),
                                        timestamp = Date(),
                                        lockingApplicationSender = device?.lockingApplicationSender,
                                        authorizationApplicationSender = device.authorisationApplicationSender,
                                        txnId = posTxnData?.txnId ?: generateTransactionId(
                                            storeCode = storeConfig.storeCode,
                                            posNumber = storeConfig.posNumber
                                        ),
                                        txnItemId = posTxnData?.txnItemId ?: UUID.randomUUID()
                                            .toString(),
                                        state = device?.state,
                                        linkedTxnId = state.linkedTxnId
                                    )
                                    launch {
                                        updateFSTransactionStatus.invoke(
                                            fuelSaleTrx,
                                            state
                                        )
                                    }
                                    launch {
                                        clearFuelSale(
                                            device,
                                            stateFlow,
                                            fdcConfig
                                        )
                                    }
                                }

                                return@fpMap fuelPumpState.copy(
                                    fuelSaleTrxState = FuelSaleTrxState(
                                        fuelSaleTrxDeviceClass = device,
                                    )
                                )
                            }
                            fuelPumpState
                        })
                    })
                }
            }
    }
}