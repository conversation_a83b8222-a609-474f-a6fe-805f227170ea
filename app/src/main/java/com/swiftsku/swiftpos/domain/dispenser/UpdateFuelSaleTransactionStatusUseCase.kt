package com.swiftsku.swiftpos.domain.dispenser

import com.pax.poslinkadmin.constant.TransactionType
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.FuelSaleTrx
import com.swiftsku.swiftpos.data.model.PostFuel
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.hasOnlyCreditPayment
import com.swiftsku.swiftpos.data.model.mergeChequeIntoCashPayment
import com.swiftsku.swiftpos.data.model.mergeCreditIntoCashPayment
import com.swiftsku.swiftpos.data.model.postFuelAmount
import com.swiftsku.swiftpos.data.model.preFuelAmount
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.DoCreditResponseDto
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.productName
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import kotlin.math.min

class UpdateFuelSaleTransactionStatusUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val transactionRepository: TransactionRepository,
    private val paxPaymentService: PaxPaymentService,
    private val paymentUseCase: PaymentUseCase
) {

    suspend operator fun invoke(
        fuelSale: FuelSaleTrx,
        fdcState: FDCState
    ) = withContext(dispatcher) {
        launch {
            // val linkedTxnId = fuelSale.linkedTxnId ?: return@launch
            val transaction = transactionRepository.getTransaction(fuelSale.txnId) ?: return@launch
            (transaction as? SaleTransaction)?.let {
                savePostFuelTransaction(fdcState, transaction, fuelSale)
            }
        }
    }

    private suspend fun savePostFuelTransaction(
        fdcState: FDCState,
        transaction: SaleTransaction,
        fuelSale: FuelSaleTrx
    ) = withContext(dispatcher) {
        launch {
            val txnItemsPostFuel = transaction.txnItems.map {
                val itemIdLastPart = it.transactionItemId.split("-").last()
                if (it is TransactionItemWithFuel && fuelSale.amount != null && fuelSale.volume != null && fuelSale.txnItemId == itemIdLastPart) {
                    return@map it.copy(
                        postFuel = PostFuel(
                            pumpNo = fuelSale.pumpNo,
                            deviceId = fuelSale.deviceId,
                            amount = fuelSale.amount,
                            volume = fuelSale.volume,
                            volume1 = fuelSale.volume1,
                            volume2 = fuelSale.volume2,
                            blendRatio = fuelSale.blendRatio,
                            product1 = fuelSale.product1?.let { fdcState.productName(it) },
                            product2 = fuelSale.product2?.let { fdcState.productName(it) },
                            productNo1 = fuelSale.product1,
                            productNo2 = fuelSale.product2,
                            unitPrice = fuelSale.unitPrice
                        )
                    )
                }
                it
            }
            var areAllFuelItemsCompleted = true
            txnItemsPostFuel.filterIsInstance<TransactionItemWithFuel>()
                .filter { it.status == TransactionItemStatus.Normal }.forEach {
                    // If any of the fuel item doesn't have postFuel, that means areAllFuelItemsCompleted is false
                    if (it.postFuel == null) {
                        areAllFuelItemsCompleted = false
                        return@forEach
                    }
                    // If all of the fuel items have postFuel but amount is not matching with preFuel amount
                    if (it.postFuel.amount != it.preFuel.amount) {
                        areAllFuelItemsCompleted = false
                        return@forEach
                    }
                }

            val isCardPayment =
                (transaction.txnPayment[TxnPaymentType.Card] as? CardPayment) != null
            val txnStatus = if (!isCardPayment && areAllFuelItemsCompleted) {
                TransactionStatus.Complete
            } else {
                TransactionStatus.FuelDispensed
            }
            val saleTxn = transaction.copy(
                txnStatus = txnStatus,
                txnItems = txnItemsPostFuel,
                statusHistory = HashMap(transaction.statusHistory.orEmpty()).apply {
                    put(Date().epochInSeconds(), txnStatus)
                }
            )
            val saved = transactionRepository.saveTransaction(saleTxn)
            if (txnStatus == TransactionStatus.FuelDispensed && transaction.hasOnlyCreditPayment()) {
                completeTxnOnCredit(saleTxn)
            } else if (saved && isCardPayment) {
                captureEpxTransaction(saleTxn)
            }
        }
    }

    private suspend fun completeTxnOnCredit(transaction: SaleTransaction) {
        var allFuelDispensed = true
        transaction.txnItems.forEach {
            if (it is TransactionItemWithFuel && it.postFuel == null) {
                allFuelDispensed = false
            }
        }
        if (!allFuelDispensed) {
            return
        }
        val creditPayment = (transaction.txnPayment[TxnPaymentType.Credit] as? CreditPayment)
        // Find total post fuel amount
        val postFuelAmount = transaction.postFuelAmount()
        // Find total pre fuel amount
        val preFuelAmount = transaction.preFuelAmount()
        val fuelShortfall = maxOf(preFuelAmount - postFuelAmount, 0.0).toFloat().to2Decimal()
        val amountToPay = (transaction.txnTotalGrandAmount - fuelShortfall).coerceAtLeast(0f)
        val updatedTxn = transaction.copy(
            txnPayment = transaction.txnPayment.toMutableMap().apply {
                creditPayment?.let {
                    if (amountToPay == 0f) {
                        this.remove(TxnPaymentType.Credit)
                    } else {
                        this[TxnPaymentType.Credit] = it.copy(amount = amountToPay.to2Decimal())
                    }
                }
            },
            txnTotalGrandAmount = transaction.txnTotalGrandAmount.minus(fuelShortfall)
                .to2Decimal(),
            txnTotalGrossAmount = transaction.txnTotalGrossAmount.minus(fuelShortfall)
                .to2Decimal(),
            txnTotalNetAmount = transaction.txnTotalNetAmount.minus(fuelShortfall)
                .to2Decimal(),
            txnStatus = TransactionStatus.Complete,
            statusHistory = HashMap(transaction.statusHistory.orEmpty()).apply {
                put(Date().epochInSeconds(), TransactionStatus.Complete)
            }
        )
        transactionRepository.saveTransaction(updatedTxn)
    }

    private suspend fun captureEpxTransaction(transaction: SaleTransaction) {
        var allFuelDispensed = true
        transaction.txnItems.filterIsInstance<TransactionItemWithFuel>()
            .filter { it.status == TransactionItemStatus.Normal }
            .forEach {
                if (it.postFuel == null) {
                    allFuelDispensed = false
                }
            }
        if (!allFuelDispensed) {
            EventUtils.recordException(Exception("Fuel module ${transaction.txnId}: Can't capture because post fuel is missing"))
            return
        }
        /*
            ToDo: This could be called multiple times from FDC listener, we need to put a check to not trigger payment request multiple times.
         */
        // If all fuel is dispensed, we can capture the final amount
        var cashPaymentTypeTemp = (transaction.txnPayment[TxnPaymentType.Cash] as? CashPayment)
        val ebtPayment = (transaction.txnPayment[TxnPaymentType.EBT] as? EBTPayment)
        val authCardPayment = (transaction.txnPayment[TxnPaymentType.Card] as? CardPayment)
        val chequePayment = (transaction.txnPayment[TxnPaymentType.Cheque] as? ChequePayment)
        val creditPayment = (transaction.txnPayment[TxnPaymentType.Credit] as? CreditPayment)
        cashPaymentTypeTemp = cashPaymentTypeTemp.mergeChequeIntoCashPayment(chequePayment)
        val cashPayment = cashPaymentTypeTemp.mergeCreditIntoCashPayment(creditPayment)
        val isEpxAuthTxn =
            authCardPayment?.epxData?.transactionType == TransactionType.AUTHORIZATION
        if (!isEpxAuthTxn) {
            EventUtils.recordException(Exception("Fuel module ${transaction.txnId}: Can't capture because epx txn is not authorized"))
            return
        }
        val authorizedAmount = authCardPayment?.amount ?: 0f
        /*
           Check how much do we need to capture. For example, consumer paid $5 through cash and
           authorised $5 on card for a $10 fuel transaction.
           If they fuel for $2 - refund $3 back and don't capture anything.
           If they fuel for $8 - capture $3
           How do we take care of EBT payment?
           We can deduct EBT amount from the txnTotalGrandAmount.
         */
        // Find total post fuel amount
        var amountToCapture = transaction.postFuelAmount()

        // Find total pre fuel amount
        val preFuelAmount = transaction.preFuelAmount()

        var txnToSave = transaction.copy()

        val diff =
            if (preFuelAmount > amountToCapture) preFuelAmount.minus(amountToCapture).toFloat().to2Decimal() else 0f

        var totalAmountToPay = transaction.txnTotalGrandAmount.minus(diff)
        ebtPayment?.let {
            totalAmountToPay = totalAmountToPay.minus(it.amount).to2Decimal()
        }
        /*
            We prefer to charge on card before the cash. So if the authorised amount is enough
            we can refund all the cash paid.
            Otherwise we capture on card first and then charge on cash and refund the pending amount.
         */
        amountToCapture = min(authorizedAmount, totalAmountToPay).toDouble().to2Decimal()

        if (amountToCapture <= 0.01) {
            EventUtils.recordException(Exception("Fuel module ${transaction.txnId}: Can't capture because amountToCapture is $amountToCapture, authorizedAmount: $authorizedAmount, totalAmountToPay: $totalAmountToPay, diff: $diff"))
            return
        }

        val paxPaymentInput = PaxPaymentInput(
            txnToSave.txnId,
            paxPaymentService.getCurrentBatchId(),
            amountToCapture.convertDollarToCentPrecisely().toString(),
            txnToSave.cashierId,
            Date().epochInSeconds(),
            null,
            TransactionType.POST_AUTHORIZATION,
            txnToSave.paymentRecord?.originalReferenceNumber
        )
        paymentUseCase.processEpxCreditRequest(
            paxPaymentInput,
            txnToSave
        ).collect { result ->
            val cardPaymentExisting = (txnToSave.txnPayment[TxnPaymentType.Card] as? CardPayment)
            when (result) {
                is Result.Error -> {
                    // Save error in transaction.txnPayment.Card.epxData
                    val cardPaymentNew = cardPaymentExisting?.copy(
                        epxData = cardPaymentExisting.epxData?.copy(
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode,
                        ) ?: EpxData(
                            transactionType = paxPaymentInput.txnType,
                            amount = amountToCapture.toFloat().to2Decimal(),
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode
                        )
                    ) ?: CardPayment(
                        amount = amountToCapture.toFloat().to2Decimal(),
                        payFacId = "",
                        brand = "",
                        epxData = EpxData(
                            transactionType = paxPaymentInput.txnType,
                            amount = amountToCapture.toFloat().to2Decimal(),
                            "",
                            "",
                            "",
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode
                        )
                    )
                    var saleTxn = txnToSave.copy(
                        txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                            this[TxnPaymentType.Card] = cardPaymentNew
                        }
                    )
                    // It was a success. Todo: Replace with LocalDetailReportRequest to get the status
                    if (result.errorCode == "100021" && result.errorMessage == "ALREADY COMPL.") {
                        saleTxn = saleTxn.copy(
                            txnTotalGrandAmount = saleTxn.txnTotalGrandAmount.minus(diff),
                            txnTotalGrossAmount = saleTxn.txnTotalGrossAmount.minus(diff),
                            txnTotalNetAmount = saleTxn.txnTotalNetAmount.minus(diff)
                        )
                        if (cashPayment == null || diff < 0.001) {
                            // Complete the txn if cash payment is not present or fuel is fully dispensed
                            saleTxn = saleTxn.copy(
                                txnStatus = TransactionStatus.Complete,
                                statusHistory = HashMap(saleTxn.statusHistory.orEmpty()).apply {
                                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                                }
                            )
                        }
                    }
                    transactionRepository.saveTransaction(saleTxn)
                }

                is Result.Success -> {
                    result.data?.let {
                        val cardPayment = cardPaymentExisting?.copy(
                            amount = amountToCapture.toFloat().to2Decimal(),
                            payFacId = it.hostInformation?.hostReferenceNumber.orEmpty(),
                            brand = it.accountInformation?.cardType?.name.orEmpty(),
                            paymentType = it.paymentEmvTag?.appLabel.orEmpty(),
                            epxData = EpxData(
                                transactionType = paxPaymentInput.txnType,
                                amount = amountToCapture.toFloat().to2Decimal(),
                                it.hostInformation?.authorizationCode.orEmpty(),
                                it.hostInformation?.hostResponseMessage.orEmpty(),
                                it.hostInformation?.hostReferenceNumber.orEmpty(),
                            )
                        ) ?: CardPayment(
                            amount = amountToCapture.toFloat().to2Decimal(),
                            payFacId = it.hostInformation?.hostReferenceNumber.orEmpty(),
                            brand = it.accountInformation?.cardType?.name.orEmpty(),
                            paymentType = it.paymentEmvTag?.appLabel.orEmpty(),
                            epxData = EpxData(
                                transactionType = paxPaymentInput.txnType,
                                amount = amountToCapture.toFloat().to2Decimal(),
                                it.hostInformation?.authorizationCode.orEmpty(),
                                it.hostInformation?.hostResponseMessage.orEmpty(),
                                it.hostInformation?.hostReferenceNumber.orEmpty(),
                            )
                        )
                        val cardInfo = CardInfo(
                            merchantId = it.hostInformation?.paymentAccountReferenceId.orEmpty(),
                            approvalCode = it.hostInformation?.authorizationCode.orEmpty(),
                            logo = it.accountInformation?.cardType?.name.orEmpty(),
                            transactionId = txnToSave.txnId,
                            account = it.accountInformation?.account.orEmpty(),
                            entry = it.accountInformation?.entryMode?.name.orEmpty(),
                            label = it.paymentEmvTag?.appLabel.orEmpty(),
                        )
                        txnToSave = txnToSave.copy(
                            txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                                this[TxnPaymentType.Card] = cardPayment
                            },
                            cardInfo = cardInfo,
                            txnTotalGrandAmount = txnToSave.txnTotalGrandAmount.minus(diff),
                            txnTotalGrossAmount = txnToSave.txnTotalGrossAmount.minus(diff),
                            txnTotalNetAmount = txnToSave.txnTotalNetAmount.minus(diff)
                        )
                        if (cashPayment == null || diff < 0.001) {
                            // Complete the txn if cash payment is not present or fuel is fully dispensed
                            txnToSave = txnToSave.copy(
                                txnStatus = TransactionStatus.Complete,
                                statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                                }
                            )
                        }
                        transactionRepository.saveTransaction(txnToSave)
                    }
                }

                is Result.Loading -> {}
            }
        }
    }
}