package com.swiftsku.swiftpos.domain.printer.dto

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CashDepositData
import com.swiftsku.swiftpos.data.model.CashWithdrawalData
import com.swiftsku.swiftpos.data.model.EbtInfo
import com.swiftsku.swiftpos.data.model.LedgerEntryData
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TxnPayment
import java.util.Date

data class PrintTxnReceiptInput(
    val storeConfig: StoreConfig,
    val change: Float,
    val txnStartTime: Date,
    val txnEndTime: Date?,
    val txnId: String,
    val cartItems: List<TransactionItem>,
    val coupon: Float,
    val subTotal: Float,
    val total: Float,
    val tax: Float,
    val loyaltyAccount: String?,
    val cardInfo: CardInfo? = null,
    val ebtInfo: EbtInfo? = null,
    val cardPayment: TxnPayment?,
    val cashPayment: TxnPayment?,
    val ebtPayment: TxnPayment?,
    val chequePayment: TxnPayment?,
    val creditPayment: TxnPayment?,
    val appliedFees: List<AppliedFee>? = null,
    val lotteryPayout: LotteryPayout?,
    val lotteryPayouts: List<LotteryPayout>?,
    val isRefund: Boolean = false,
    val isVoid: Boolean = false,
    val payoutData: Payout? = null,
    val cashWithdrawalData: CashWithdrawalData? = null,
    val cashDepositData: CashDepositData? = null,
    val ledgerEntryData: LedgerEntryData? = null,
)
