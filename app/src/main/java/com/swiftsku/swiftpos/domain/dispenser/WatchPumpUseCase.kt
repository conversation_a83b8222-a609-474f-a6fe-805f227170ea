package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.GetCurrentFuelingStatusPOSdataType
import com.fdc.core.types.GetCurrentFuelingStatusRequestType
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.GetCurrentFuelingStatusUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject


class WatchPumpUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getCurrentFuelStatus: GetCurrentFuelingStatusUseCase
) {
    private val watchPumpInvokeJobs: HashMap<Int, Job?> = hashMapOf()
    private val watchPumpObservingJobs: HashMap<Int, Job?> = hashMapOf()

    fun isWatching(deviceId: Int) = watchPumpObservingJobs[deviceId] != null

    fun stopWatchingPump(deviceId: Int) {
        watchPumpInvokeJobs[deviceId]?.let {
            it.cancel()
            watchPumpInvokeJobs.remove(deviceId)
        }
        watchPumpObservingJobs[deviceId]?.let {
            it.cancel()
            watchPumpObservingJobs.remove(deviceId)
        }
    }

    suspend operator fun invoke(
        pumpDeviceId: Int,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {
        val observeJob = launch {
            getCurrentFuelStatus
                .currentFuelingStatus
                .collectLatest { currentStatus ->
                    if (!currentStatus.isForCurrentDevice(workstationId = fdcConfig.workstationID)) {
                        return@collectLatest
                    }

                    stateFlow.update { state ->
                        state.copy(dispensers = state.dispensers.map { dispenserState ->
                            dispenserState.copy(fuelPump = dispenserState.fuelPump.map fpMap@{ fuelPumpState ->
                                val deviceClass = currentStatus.fdCdata.deviceClass
                                if (pumpDeviceId == fuelPumpState.deviceId) {
                                    return@fpMap fuelPumpState.copy(
                                        currentFuelStatus = fuelPumpState.currentFuelStatus?.copy(
                                            currentAmount = deviceClass.currentAmount,
                                            currentVolume = deviceClass.currentVolume
                                        )
                                    )
                                }
                                fuelPumpState
                            })
                        })
                    }
                }
        }
        watchPumpObservingJobs[pumpDeviceId] = observeJob

        val invokeJob = launch {
            while (isActive) {
                getCurrentFuelStatus.invoke(GetCurrentFuelingStatusRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = generatePumpRequestId(pumpDeviceId)
                    poSdata = GetCurrentFuelingStatusPOSdataType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            deviceID = pumpDeviceId
                            type = Type.FP
                        }
                    }
                })
                delay(1000)
            }
        }
        watchPumpInvokeJobs[pumpDeviceId] = invokeJob
    }
}