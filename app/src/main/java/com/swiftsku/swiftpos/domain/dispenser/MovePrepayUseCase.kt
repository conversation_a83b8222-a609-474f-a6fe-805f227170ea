package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.AuthorizeFuelPointPOSdataType
import com.fdc.core.types.AuthorizeFuelPointRequestType
import com.fdc.core.types.AuthorizeRequestDeviceClassType
import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.FreeFuelPointRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.POSTransDataType
import com.fdc.core.types.POSdataBaseType
import com.fdc.core.types.TerminateFuelPointRequestType
import com.fdc.core.types.TranTypeEnum
import com.fdc.core.types.TrueFalse
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.AuthorizeFuelPointRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.TerminateFPRequestUseCase
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.PreFuel
import com.swiftsku.swiftpos.data.model.PumpMeta
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDC_MAX_PREPAY
import com.swiftsku.swiftpos.ui.dashboard.main.state.FPLockState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.PosTxnData
import com.swiftsku.swiftpos.ui.dashboard.main.state.addFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.deletedFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.posTxnDataToFdc
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/***
 * 1 Free the pump
 * 2 On success authorize the new pump
 * 3 On success update the txnItems in the transaction
 *   1 First change the previous itemLine to Deleted Status
 *   2 Add a new itemLine with new pump details
 */


class MovePrepayUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val freeFP: FreeFuelPointUseCase,
    private val terminateFP: TerminateFPRequestUseCase,
    private val authorizePump: AuthorizeFuelPointRequestUseCase,
    private val transactionRepository: TransactionRepository
) {
    suspend operator fun invoke(
        previousPump: FuelPumpState,
        newPump: FuelPumpState,
        fdcState: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig,
        storeLevelConfig: StoreUsers?
    ) = withContext(dispatcher) {

        var newFpLockState: FPLockState? = null
        val newTxnItemId = UUID.randomUUID().toString()
        val txn = transactionRepository.getPendingFuelTransactionsForPump(
            deviceId = previousPump.deviceId, status = listOf(TransactionStatus.FuelAuthorized)
        )
        if (txn == null) {
            EventUtils.recordException(
                Exception("No pending fuel transaction found for pump ${previousPump.deviceId}")
            )
        }

        txn?.let {
            var fuelMeta = txn.fuelMeta
            if (fuelMeta != null) {
                fuelMeta =
                    fuelMeta.copy(authorizedPumps = fuelMeta.authorizedPumps.filter { it.deviceId != previousPump.deviceId }
                        .plus(PumpMeta(newPump.deviceId)))
            }
            var oldPreFuel: PreFuel? = null
            var txnItems = txn.txnItems.filterIsInstance<TransactionItemWithFuel>()
                .filter { it.status == TransactionItemStatus.Normal }
                .map {
                    if (it.preFuel.deviceId == previousPump.deviceId) {
                        newFpLockState = FPLockState(
                            lockedAmount = it.preFuel.amount.to2Decimal(),
                            lockedVolume = (it.preFuel.volume ?: 0.0),
                            lockedPrice = it.preFuel.unitPrice,
                            lockedPumpState = newPump,
                            transactionItemId = newTxnItemId
                        )
                        oldPreFuel = it.preFuel
                        return@map it.copy(status = TransactionItemStatus.Deleted)
                    }
                    it
                }
            newFpLockState?.let { newFpLockState ->
                txnItems = txnItems.plus(
                    TransactionItemWithFuel(
                        transactionItemId = newFpLockState.transactionItemId, preFuel = PreFuel(
                            deviceId = newPump.deviceId,
                            pumpNo = newPump.pumpNo,
                            amount = newFpLockState.lockedAmount,
                            volume = newFpLockState.lockedVolume,
                            unitPrice = newFpLockState.lockedPrice,
                            productNo = oldPreFuel?.productNo
                        )
                    )
                )
                val saved = transactionRepository.saveTransaction(
                    txn.copy(
                        txnStatus = TransactionStatus.FuelAuthorized,
                        statusHistory = HashMap(txn.statusHistory.orEmpty()).apply {
                            put(Date().epochInSeconds(), TransactionStatus.FuelAuthorized)
                        },
                        fuelMeta = fuelMeta,
                        txnItems = txnItems
                    )
                )
                if (saved) {
                    val requestId = generatePumpRequestId(newPump.pumpNo)

                    launch {
                        terminateFP.terminateFPResponse.collectLatest { response ->
                            if (response.isForCurrentDevice(
                                    workstationId = fdcConfig.workstationID,
                                    requestID = requestId
                                ) && response.overallResult == OverallResult.SUCCESS
                            ) {
                                launch {
                                    freePump(
                                        previousPump = previousPump,
                                        newPump = newPump,
                                        fdcState = fdcState,
                                        fdcConfig = fdcConfig,
                                        storeLevelConfig = storeLevelConfig,
                                        newFpLockState = newFpLockState,
                                        saleTxn = txn,
                                        newTxnItemId = newFpLockState.transactionItemId
                                    )
                                }
                            }
                        }
                    }

                    launch {
                        terminateFP.invoke(TerminateFuelPointRequestType().apply {
                            applicationSender = fdcConfig.applicationSender
                            workstationID = fdcConfig.workstationID
                            requestID = requestId
                            poSdata = POSdataBaseType().apply {
                                posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                                deviceClass = DeviceClassType().apply {
                                    type = Type.FP
                                    deviceID = previousPump.deviceId
                                }
                            }
                        })
                    }
                }
            }
        }
    }

    private suspend fun freePump(
        previousPump: FuelPumpState,
        newPump: FuelPumpState,
        fdcState: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig,
        storeLevelConfig: StoreUsers?,
        newFpLockState: FPLockState,
        saleTxn: SaleTransaction,
        newTxnItemId: String
    ) = withContext(dispatcher) {

        val requestId = generatePumpRequestId(newPump.pumpNo)


        launch {
            freeFP.freeFPResponse.collectLatest { response ->
                if (response.isForCurrentDevice(
                        workstationId = fdcConfig.workstationID, requestID = requestId
                    ) && response.overallResult == OverallResult.SUCCESS
                ) {

                    launch {
                        fdcState.update {
                            it.deletedFpLock(previousPump.deviceId)
                        }
                    }

                    launch {
                        authorizePump(
                            deviceId = newPump.deviceId,
                            newPump = newPump,
                            previouslySavedTransaction = saleTxn,
                            newFpLockState = newFpLockState,
                            fdcState = fdcState,
                            newTxnItemId = newTxnItemId,
                            fdcConfig = fdcConfig,
                            storeLevelConfig = storeLevelConfig
                        )
                    }
                }
            }
        }

        launch {
            freeFP(FreeFuelPointRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = requestId
                poSdata = POSdataBaseType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = DeviceClassType().apply {
                        type = Type.FP
                        deviceID = previousPump.deviceId
                    }
                }
            })
        }
    }

    private suspend fun authorizePump(
        deviceId: Int,
        trnType: TranTypeEnum = TranTypeEnum.PREPAY,
        newPump: FuelPumpState,
        fpFuelMode: Int = 1,
        previouslySavedTransaction: SaleTransaction,
        newFpLockState: FPLockState,
        fdcState: MutableStateFlow<FDCState>,
        newTxnItemId: String,
        fdcConfig: FDCConfig,
        storeLevelConfig: StoreUsers?
    ) = withContext(dispatcher) {

        val requestId = generatePumpRequestId(newPump.pumpNo)

        launch {
            authorizePump.authorizeFuelResponse.collectLatest { response ->
                if (response.isForCurrentDevice(
                        workstationId = fdcConfig.workstationID, requestID = requestId
                    ) && response.overallResult == OverallResult.SUCCESS
                ) {
                    launch {
                        fdcState.update { it.addFpLock(newFpLockState) }
                    }
                }
            }
        }
        launch {
            val posData = AuthorizeRequestDeviceClassType().apply {
                lockFuelSaleTrx = TrueFalse.TRUE
                type = Type.FP
                deviceID = deviceId
                posTransData = POSTransDataType().apply {
                    tranType = trnType
                    epsstan = 1.toBigInteger()
                    fuelMode = AuthorizeRequestDeviceClassType.FuelMode().apply {
                        modeNo = fpFuelMode
                    }
                    posTransactionData = posTxnDataToFdc(
                        PosTxnData(previouslySavedTransaction.txnId, newTxnItemId)
                    )
                }
            }

            val maxPostPayAmount: Double =
                if (storeLevelConfig?.maxFuelPostpayLimitCents != null) storeLevelConfig.maxFuelPostpayLimitCents / 100.0
                else FDC_MAX_PREPAY

            if (newFpLockState.lockedAmount > 0) {
                posData.maxTrxAmount = newFpLockState.lockedAmount.toBigDecimal()
            } else {
                posData.maxTrxAmount = maxPostPayAmount.toBigDecimal()
            }

            if (newFpLockState.lockedVolume > 0) {
                posData.maxTrxVolume = newFpLockState.lockedVolume.toBigDecimal()
            }

            authorizePump(AuthorizeFuelPointRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = requestId
                poSdata = AuthorizeFuelPointPOSdataType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = posData
                }
            })
        }
    }
}