package com.swiftsku.swiftpos.domain.dispenser

import com.pax.poslinkadmin.constant.TransactionType
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.getAmountsToBePaidByMop
import com.swiftsku.swiftpos.data.model.getUpdatedTxnPayment
import com.swiftsku.swiftpos.data.model.isEpxCaptured
import com.swiftsku.swiftpos.data.model.mergeChequeIntoCashPayment
import com.swiftsku.swiftpos.data.model.postFuelAmount
import com.swiftsku.swiftpos.data.model.preFuelAmount
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import kotlin.math.min


class PendingFuelTxnUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val transactionRepository: TransactionRepository,
    private val paxPaymentService: PaxPaymentService,
    private val paymentUseCase: PaymentUseCase
) {
    /**
     * There could be scenarios where consumer didn't bother or they forgot to get the change back
     * after fuelling, in which case txn will be in FuelDispensed state.
     * There could also be the case when txn is FuelAuthorized but consumer didn't fuel at all.
     *
     * For cash txn:
     *  Since cashier has already collected the money, we can mark that the change was given to the consumer
     *  and close the txn. There will be extra cash in the drawer and it'll create cash "overage".
     *
     * For card txn:
     *  With EPX, we haven't captured the amount yet, so in case consumer didn't fuel at all and has nothing to
     *  pay for, we can void that transaction.
     *  In case, they took some merchandise or fuelled for some amount, we can capture only that amount
     *  and complete the transaction.
     *
     *  For credit txn:
     *  Update the existing CreditPayment data, when txn is saved, it'll create the ledger entry.
     */
    suspend fun closePendingFuelTxn(txn: SaleTransaction): Boolean {
        return withContext(dispatcher) {
            val cashPaymentTypeTemp = (txn.txnPayment[TxnPaymentType.Cash] as? CashPayment)
            val ebtPayment = (txn.txnPayment[TxnPaymentType.EBT] as? EBTPayment)
            val cardPayment = (txn.txnPayment[TxnPaymentType.Card] as? CardPayment)
            val chequePayment = txn.txnPayment[TxnPaymentType.Cheque] as? ChequePayment
            val creditPayment = txn.txnPayment[TxnPaymentType.Credit] as? CreditPayment
            /*
            We can consider check as a cash payment, so instead of handling it separately we can
            increase the cashPayment amount
             */
            val cashPayment = cashPaymentTypeTemp.mergeChequeIntoCashPayment(chequePayment)

            // Find total post fuel amount
            val postFuelAmount = txn.postFuelAmount()

            // Find total pre fuel amount
            val preFuelAmount = txn.preFuelAmount()

            var updatedTxn: SaleTransaction

            val fuelShortfall =
                if (preFuelAmount > postFuelAmount) (preFuelAmount - postFuelAmount).toFloat() else 0f
            var amountToPay = (txn.txnTotalGrandAmount - fuelShortfall).coerceAtLeast(0f)
            ebtPayment?.let {
                amountToPay = (amountToPay - it.amount).coerceAtLeast(0f)
            }

            val amountsToBePaidByMop = getAmountsToBePaidByMop(
                cardPayment, creditPayment, cashPayment, amountToPay
            )
            updatedTxn = getUpdatedTxnPayment(txn, amountsToBePaidByMop)
            val epxCaptured = cardPayment.isEpxCaptured()
            if (!epxCaptured && cardPayment != null && amountsToBePaidByMop.cardAmount > 0) {
                captureEpxTransaction(
                    updatedTxn,
                    cardPayment.amount,
                    amountsToBePaidByMop.cardAmount,
                    fuelShortfall
                )
            } else {
                updatedTxn = updatedTxn.copy(
                    txnTotalGrandAmount = updatedTxn.txnTotalGrandAmount.minus(fuelShortfall)
                        .to2Decimal(),
                    txnTotalGrossAmount = updatedTxn.txnTotalGrossAmount.minus(fuelShortfall)
                        .to2Decimal(),
                    txnTotalNetAmount = updatedTxn.txnTotalNetAmount.minus(fuelShortfall)
                        .to2Decimal(),
                    txnStatus = TransactionStatus.Complete,
                    statusHistory = HashMap(updatedTxn.statusHistory.orEmpty()).apply {
                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                    }
                )
                transactionRepository.saveTransaction(updatedTxn)
            }
            return@withContext true
        }
    }

    private suspend fun captureEpxTransaction(
        txn: SaleTransaction,
        authorizedAmount: Float,
        amountToPay: Float,
        diffInFuelAmount: Float
    ) {
        val fuelAmountToCapture = min(authorizedAmount, amountToPay).toDouble()
        val paxPaymentInput = PaxPaymentInput(
            txn.txnId,
            paxPaymentService.getCurrentBatchId(),
            fuelAmountToCapture.convertDollarToCentPrecisely().toString(),
            txn.cashierId,
            Date().epochInSeconds(),
            null,
            TransactionType.POST_AUTHORIZATION,
            txn.paymentRecord?.originalReferenceNumber
        )
        paymentUseCase.processEpxCreditRequest(
            paxPaymentInput,
            txn
        ).collect { result ->
            val cardPaymentExisting = (txn.txnPayment[TxnPaymentType.Card] as? CardPayment)
            when (result) {
                is Result.Error -> {
                    // Save error in transaction.txnPayment.Card.epxData
                    val cardPaymentNew = cardPaymentExisting?.copy(
                        epxData = cardPaymentExisting.epxData?.copy(
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode,
                        ) ?: EpxData(
                            transactionType = paxPaymentInput.txnType,
                            amount = fuelAmountToCapture.toFloat().to2Decimal(),
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode
                        )
                    ) ?: CardPayment(
                        amount = fuelAmountToCapture.toFloat().to2Decimal(),
                        payFacId = "",
                        brand = "",
                        epxData = EpxData(
                            transactionType = paxPaymentInput.txnType,
                            amount = fuelAmountToCapture.toFloat().to2Decimal(),
                            "",
                            "",
                            "",
                            errorMessage = result.errorMessage,
                            errorCode = result.errorCode
                        )
                    )
                    var saleTxn = txn.copy(
                        txnPayment = txn.txnPayment.toMutableMap().apply {
                            this[TxnPaymentType.Card] = cardPaymentNew
                        }
                    )
                    // It was a success. Todo: Replace with LocalDetailReportRequest to get the status
                    if (result.errorCode == "100021" && result.errorMessage == "ALREADY COMPL.") {
                        saleTxn = saleTxn.copy(
                            txnTotalGrandAmount = saleTxn.txnTotalGrandAmount.minus(diffInFuelAmount),
                            txnTotalGrossAmount = saleTxn.txnTotalGrossAmount.minus(diffInFuelAmount),
                            txnTotalNetAmount = saleTxn.txnTotalNetAmount.minus(diffInFuelAmount),
                            txnStatus = TransactionStatus.Complete,
                            statusHistory = HashMap(txn.statusHistory.orEmpty()).apply {
                                put(Date().epochInSeconds(), TransactionStatus.Complete)
                            }
                        )
                    }
                    transactionRepository.saveTransaction(saleTxn)
                }

                is Result.Success -> {
                    result.data?.let {
                        val cardPayment = cardPaymentExisting?.copy(
                            amount = fuelAmountToCapture.toFloat().to2Decimal(),
                            payFacId = it.hostInformation?.hostReferenceNumber.orEmpty(),
                            brand = it.accountInformation?.cardType?.name.orEmpty(),
                            paymentType = it.paymentEmvTag?.appLabel.orEmpty(),
                            epxData = EpxData(
                                transactionType = paxPaymentInput.txnType,
                                amount = fuelAmountToCapture.toFloat().to2Decimal(),
                                it.hostInformation?.authorizationCode.orEmpty(),
                                it.hostInformation?.hostResponseMessage.orEmpty(),
                                it.hostInformation?.hostReferenceNumber.orEmpty(),
                            )
                        ) ?: CardPayment(
                            amount = fuelAmountToCapture.toFloat().to2Decimal(),
                            payFacId = it.hostInformation?.hostReferenceNumber.orEmpty(),
                            brand = it.accountInformation?.cardType?.name.orEmpty(),
                            paymentType = it.paymentEmvTag?.appLabel.orEmpty(),
                            epxData = EpxData(
                                transactionType = paxPaymentInput.txnType,
                                amount = fuelAmountToCapture.toFloat().to2Decimal(),
                                it.hostInformation?.authorizationCode.orEmpty(),
                                it.hostInformation?.hostResponseMessage.orEmpty(),
                                it.hostInformation?.hostReferenceNumber.orEmpty(),
                            )
                        )
                        val cardInfo = CardInfo(
                            merchantId = it.hostInformation?.paymentAccountReferenceId.orEmpty(),
                            approvalCode = it.hostInformation?.authorizationCode.orEmpty(),
                            logo = it.accountInformation?.cardType?.name.orEmpty(),
                            transactionId = txn.txnId,
                            account = it.accountInformation?.account.orEmpty(),
                            entry = it.accountInformation?.entryMode?.name.orEmpty(),
                            label = it.paymentEmvTag?.appLabel.orEmpty(),
                        )
                        val saleTxn = txn.copy(
                            txnPayment = txn.txnPayment.toMutableMap().apply {
                                this[TxnPaymentType.Card] = cardPayment
                            },
                            cardInfo = cardInfo,
                            txnTotalGrandAmount = txn.txnTotalGrandAmount.minus(diffInFuelAmount)
                                .to2Decimal(),
                            txnTotalGrossAmount = txn.txnTotalGrossAmount.minus(diffInFuelAmount)
                                .to2Decimal(),
                            txnTotalNetAmount = txn.txnTotalNetAmount.minus(diffInFuelAmount)
                                .to2Decimal(),
                            txnStatus = TransactionStatus.Complete,
                            statusHistory = HashMap(txn.statusHistory.orEmpty()).apply {
                                put(Date().epochInSeconds(), TransactionStatus.Complete)
                            }
                        )
                        transactionRepository.saveTransaction(saleTxn)
                    }
                }

                is Result.Loading -> {}
            }
        }
    }
}