package com.swiftsku.swiftpos.domain.dispenser

import com.pax.poslinkadmin.constant.TransactionType
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.VoidTransaction
import com.swiftsku.swiftpos.data.model.mergeChequeIntoCashPayment
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.min


class PendingFuelTxnUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val transactionRepository: TransactionRepository,
    private val paxPaymentService: PaxPaymentService,
    private val paymentUseCase: PaymentUseCase
) {
    /**
     * There could be scenarios where consumer didn't bother or they forgot to get the change back
     * after fuelling, in which case txn will be in FuelDispensed state.
     * There could also be the case when txn is FuelAuthorized but consumer didn't fuel at all.
     *
     * For cash txn:
     *  Since cashier has already collected the money, we can mark that the change was given to the consumer
     *  and close the txn. There will be extra cash in the drawer and it'll create cash "overage".
     *
     * For card txn:
     *  With EPX, we haven't captured the amount yet, so in case consumer didn't fuel at all and has nothing to
     *  pay for, we can void that transaction.
     *  In case, they took some merchandise or fuelled for some amount, we can capture only that amount
     *  and complete the transaction.
     */
    suspend fun closePendingFuelTxn(txn: SaleTransaction): Boolean {
        return withContext(dispatcher) {
            val cashPaymentTypeTemp = (txn.txnPayment[TxnPaymentType.Cash] as? CashPayment)
            val ebtPayment = (txn.txnPayment[TxnPaymentType.EBT] as? EBTPayment)
            val cardPayment = (txn.txnPayment[TxnPaymentType.Card] as? CardPayment)
            val chequePayment = txn.txnPayment[TxnPaymentType.Cheque] as? ChequePayment
            /*
            We can consider check as a cash payment, so instead of handling it separately we can
            increase the cashPayment amount
             */
            val cashPayment = cashPaymentTypeTemp.mergeChequeIntoCashPayment(chequePayment)

            // Find total post fuel amount
            val fuelAmountToCapture = txn.txnItems.filterIsInstance<TransactionItemWithFuel>()
                .filter { it.status == TransactionItemStatus.Normal }
                .sumOf { it.postFuel?.amount ?: 0.0 }

            // Find total pre fuel amount
            val preFuelAmount = txn.txnItems.filterIsInstance<TransactionItemWithFuel>()
                .filter { it.status == TransactionItemStatus.Normal }
                .sumOf { it.preFuel.amount }

            var txnToSave = txn.copy()

            val diff =
                if (preFuelAmount > fuelAmountToCapture) (preFuelAmount - fuelAmountToCapture).toFloat() else 0f

            if (abs(diff - 0) > 0.01) {
                var amountToPay = txn.txnTotalGrandAmount.minus(diff)
                ebtPayment?.let {
                    amountToPay = amountToPay.minus(it.amount)
                }
                when {
                    /*
                        Consumer purchased some merchandise and fuel and paid x through cash
                        and y through card. But they didn't fuel anything. We prefer to charge on card
                        before cash.
                        So here we need to check
                        1. If card payment is enough to cover merchandise
                        2. If card doesn't cover it, capture the remaining amount with cash.
                     */
                    cashPayment != null && cardPayment != null -> {
                        // Handle the case when card payment was already captured, just need to give the change back.
                        val captured =
                            cardPayment.epxData?.transactionType == TransactionType.POST_AUTHORIZATION
                        if (captured) {
                            val capturedAmount = cardPayment.amount
                            if (capturedAmount >= txn.txnTotalGrandAmount) {
                                // refund full cash
                                txnToSave = txnToSave.copy(
                                    txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                                        this[TxnPaymentType.Cash] =
                                            cashPayment.copy(change = cashPayment.tender)
                                    },
                                    txnStatus = TransactionStatus.Complete,
                                    statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                                    }
                                )
                            } else {
                                // Deduct remaining balance from the cash tender
                                val remainingAmount = txn.txnTotalGrandAmount - capturedAmount
                                val change = cashPayment.tender - remainingAmount
                                txnToSave = txnToSave.copy(
                                    txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                                        this[TxnPaymentType.Cash] =
                                            cashPayment.copy(change = change.to2Decimal())
                                    },
                                    txnStatus = TransactionStatus.Complete,
                                    statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                                    }
                                )
                            }
                            transactionRepository.saveTransaction(txnToSave)
                        }
                        val authorisedAmt = cardPayment.amount
                        if (abs(amountToPay - 0) > 0.01) {
                            // Process the card payment and handle the pending balance after success.
                            captureEpxTransaction(
                                txnToSave,
                                authorisedAmt,
                                amountToPay,
                                diff
                            )
                        } else {
                            // Case when no fuel is dispensed, so nothing to capture + return full cash
                            txnToSave = txnToSave.copy(
                                txnTotalGrandAmount = txnToSave.txnTotalGrandAmount.minus(diff).to2Decimal(),
                                txnTotalGrossAmount = txnToSave.txnTotalGrossAmount.minus(diff).to2Decimal(),
                                txnTotalNetAmount = txnToSave.txnTotalNetAmount.minus(diff).to2Decimal(),
                                txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                                    this[TxnPaymentType.Cash] =
                                        cashPayment.copy(change = cashPayment.tender)
                                },
                                txnStatus = TransactionStatus.Complete,
                                statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                                }
                            )
                            transactionRepository.saveTransaction(txnToSave)
                        }
                    }

                    /*
                        Consumer paid in cash and fuelled less, so we can assume cashier paid them back
                        and complete the transaction.
                     */
                    cashPayment != null -> {
                        // diff should be reduced from the totals and added to the change
                        txnToSave = txnToSave.copy(
                            txnTotalGrandAmount = txnToSave.txnTotalGrandAmount.minus(diff).to2Decimal(),
                            txnTotalGrossAmount = txnToSave.txnTotalGrossAmount.minus(diff).to2Decimal(),
                            txnTotalNetAmount = txnToSave.txnTotalNetAmount.minus(diff).to2Decimal(),
                            txnPayment = txnToSave.txnPayment.toMutableMap().apply {
                                this[TxnPaymentType.Cash] = cashPayment.copy(change = diff.to2Decimal())
                            },
                            txnStatus = TransactionStatus.Complete,
                            statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                put(Date().epochInSeconds(), TransactionStatus.Complete)
                            }
                        )
                        transactionRepository.saveTransaction(txnToSave)
                    }

                    /*
                        Consumer authorised the payment but didn't fuel anything.
                        Consider a case where they bought merchandise with it.
                     */
                    cardPayment != null -> {
                        val captured =
                            cardPayment.epxData?.transactionType == TransactionType.POST_AUTHORIZATION
                        if (captured) {
                            /*
                               Ideally this should never happen, if the amount is captured,
                               txn should be completed.
                             */
                            txnToSave = txnToSave.copy(
                                txnStatus = TransactionStatus.Complete,
                                statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                                }
                            )
                            transactionRepository.saveTransaction(txnToSave)
                        }
                        if (abs(amountToPay - 0) > 0.01) {
                            captureEpxTransaction(
                                txnToSave,
                                cardPayment.amount,
                                amountToPay,
                                diff
                            )
                        } else {
                            voidTransaction(txnToSave)
                        }
                    }

                    else -> {}
                }
            } else {
                /*
                    A transaction with 0 diff can also be pending if there was a payment failure
                    when it was first received from FDC.
                 */
                var amountToPay = txn.txnTotalGrandAmount
                ebtPayment?.let {
                    amountToPay = amountToPay.minus(it.amount)
                }
                when {
                    /*
                        This shouldn't happen because it would be handled already.
                        So just mark the transaction Complete.
                     */
                    cashPayment != null -> {
                        // diff should be reduced from the totals and added to the change
                        txnToSave = txnToSave.copy(
                            txnStatus = TransactionStatus.Complete,
                            statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                                put(Date().epochInSeconds(), TransactionStatus.Complete)
                            }
                        )
                        transactionRepository.saveTransaction(txnToSave)
                    }

                    /*
                        This will take care of both, card only and card+cash transaction.
                     */
                    cardPayment != null -> {
                        captureEpxTransaction(
                            txnToSave,
                            cardPayment.amount,
                            amountToPay,
                            diff
                        )
                    }
                }
                txnToSave = txnToSave.copy(
                    txnStatus = TransactionStatus.Complete,
                    statusHistory = HashMap(txnToSave.statusHistory.orEmpty()).apply {
                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                    }
                )
                transactionRepository.saveTransaction(txnToSave)
            }
            return@withContext true
        }
    }

    private suspend fun voidTransaction(txnToSave: SaleTransaction): Boolean {
        val voidTxn = VoidTransaction(
            txnStartTime = txnToSave.txnStartTime,
            txnEndTime = Date(),
            cashierId = txnToSave.cashierId,
            txnStatus = TransactionStatus.Complete,
            statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.Complete),
            txnItems = txnToSave.txnItems,
            txnId = txnToSave.txnId,
            txnTotalGrandAmount = txnToSave.txnTotalGrandAmount,
            txnTotalGrossAmount = txnToSave.txnTotalGrossAmount,
            txnTotalNetAmount = txnToSave.txnTotalNetAmount,
            txnTotalTaxNetAmount = txnToSave.txnTotalTaxNetAmount
        )
        return transactionRepository.saveTransaction(voidTxn)
    }

    private suspend fun captureEpxTransaction(
        txn: SaleTransaction,
        authorizedAmount: Float,
        amountToPay: Float,
        diffInFuelAmount: Float
    ) {
        val fuelAmountToCapture = min(authorizedAmount, amountToPay).toDouble()
        val paxPaymentInput = PaxPaymentInput(
            txn.txnId,
            paxPaymentService.getCurrentBatchId(),
            fuelAmountToCapture.convertDollarToCentPrecisely().toString(),
            txn.cashierId,
            Date().epochInSeconds(),
            null,
            TransactionType.POST_AUTHORIZATION,
            txn.paymentRecord?.originalReferenceNumber
        )
        paymentUseCase.processEpxCreditRequest(
            paxPaymentInput,
            txn
        ).collect { result ->
            when (result) {
                is Result.Error -> {
                    // Save error in transaction.txnPayment.Card.epxData
                    var cardPayment =
                        (txn.txnPayment[TxnPaymentType.Card] as? CardPayment)
                    val epxData = cardPayment?.epxData?.copy(
                        errorMessage = result.errorMessage,
                        errorCode = result.errorCode
                    )
                    cardPayment = cardPayment?.copy(epxData = epxData)
                    cardPayment?.let {
                        val saleTxn = txn.copy(
                            txnPayment = txn.txnPayment.toMutableMap().apply {
                                this[TxnPaymentType.Card] = cardPayment
                            }
                        )
                        transactionRepository.saveTransaction(saleTxn)
                    }
                }

                is Result.Success -> {
                    result.data?.let {
                        val cardPayment = CardPayment(
                            amount = fuelAmountToCapture.toFloat().to2Decimal(),
                            payFacId = it.hostInformation?.hostReferenceNumber.orEmpty(),
                            brand = it.accountInformation?.cardType?.name.orEmpty(),
                            paymentType = it.paymentEmvTag?.appLabel.orEmpty(),
                            epxData = EpxData(
                                transactionType = paxPaymentInput.txnType,
                                it.hostInformation?.authorizationCode.orEmpty(),
                                it.hostInformation?.hostResponseMessage.orEmpty(),
                                it.hostInformation?.hostReferenceNumber.orEmpty(),
                            )
                        )
                        val cardInfo = CardInfo(
                            merchantId = it.hostInformation?.paymentAccountReferenceId.orEmpty(),
                            approvalCode = it.hostInformation?.authorizationCode.orEmpty(),
                            logo = it.accountInformation?.cardType?.name.orEmpty(),
                            transactionId = txn.txnId,
                            account = it.accountInformation?.account.orEmpty(),
                            entry = it.accountInformation?.entryMode?.name.orEmpty(),
                            label = it.paymentEmvTag?.appLabel.orEmpty(),
                        )
                        var saleTxn = txn.copy(
                            txnPayment = txn.txnPayment.toMutableMap().apply {
                                this[TxnPaymentType.Card] = cardPayment
                            },
                            cardInfo = cardInfo,
                            txnTotalGrandAmount = txn.txnTotalGrandAmount.minus(diffInFuelAmount)
                                .to2Decimal(),
                            txnTotalGrossAmount = txn.txnTotalGrossAmount.minus(diffInFuelAmount)
                                .to2Decimal(),
                            txnTotalNetAmount = txn.txnTotalNetAmount.minus(diffInFuelAmount)
                                .to2Decimal()
                        )
                        (txn.txnPayment[TxnPaymentType.Cash] as? CashPayment)?.let {
                            if (amountToPay < authorizedAmount) {
                                // Card payment covered the expenses, so refund complete cash payment
                                saleTxn = saleTxn.copy(
                                    txnPayment = saleTxn.txnPayment.toMutableMap().apply {
                                        this[TxnPaymentType.Cash] = it.copy(change = it.tender)
                                    },
                                    txnStatus = TransactionStatus.Complete,
                                    statusHistory = HashMap(saleTxn.statusHistory.orEmpty()).apply {
                                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                                    }
                                )
                            } else if (amountToPay > authorizedAmount) {
                                // Card payment is insufficient, charge remaining amount on cash
                                saleTxn = saleTxn.copy(
                                    txnPayment = saleTxn.txnPayment.toMutableMap().apply {
                                        this[TxnPaymentType.Cash] = it.copy(
                                            change = it.tender.minus(amountToPay - authorizedAmount)
                                                .to2Decimal()
                                        )
                                    },
                                    txnStatus = TransactionStatus.Complete,
                                    statusHistory = HashMap(saleTxn.statusHistory.orEmpty()).apply {
                                        put(Date().epochInSeconds(), TransactionStatus.Complete)
                                    }
                                )
                            }
                        } ?: run {
                            saleTxn = saleTxn.copy(
                                txnStatus = TransactionStatus.Complete,
                                statusHistory = HashMap(saleTxn.statusHistory.orEmpty()).apply {
                                    put(Date().epochInSeconds(), TransactionStatus.Complete)
                                }
                            )
                        }
                        transactionRepository.saveTransaction(saleTxn)
                    }
                }

                is Result.Loading -> {}
            }
        }
    }
}