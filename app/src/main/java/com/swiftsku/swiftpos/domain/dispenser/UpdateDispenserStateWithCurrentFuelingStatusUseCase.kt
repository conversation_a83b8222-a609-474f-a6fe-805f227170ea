package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.GetCurrentFuelingStatusPOSdataType
import com.fdc.core.types.GetCurrentFuelingStatusRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.Type
import com.swiftsku.fdc.core.di.usecases.dispenser.GetCurrentFuelingStatusUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelingStatus
import com.swiftsku.swiftpos.data.model.POSTransactionData
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject

class UpdateDispenserStateWithCurrentFuelingStatusUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val getCurrentFuelingStatusUseCase: GetCurrentFuelingStatusUseCase
) {


    suspend operator fun invoke(
        state: FDCState,
        stateFlow: MutableStateFlow<FDCState>,
        fdcConfig: FDCConfig
    ) = withContext(dispatcher) {

        state.dispensers.forEach { dispenser ->
            dispenser
                .fuelPump
                .forEach { eachPump ->

                    val pumpRequestId = generatePumpRequestId(eachPump.pumpNo)

                    launch {
                        getCurrentFuelingStatusUseCase
                            .currentFuelingStatus
                            .collectLatest { currentFuelingStatus ->
                                if (
                                    currentFuelingStatus.isForCurrentDevice(
                                        workstationId = fdcConfig.workstationID,
                                    ) &&
                                    currentFuelingStatus.overallResult == OverallResult.SUCCESS
                                ) {
                                    stateFlow.update { updateState ->
                                        updateState.copy(
                                            dispensers = updateState
                                                .dispensers
                                                .map { dsp ->
                                                    dsp.copy(
                                                        fuelPump = dsp
                                                            .fuelPump
                                                            .map fuelMap@{ fp ->
                                                                if (fp.deviceId == eachPump.deviceId) {
                                                                    return@fuelMap fp.copy(
                                                                        currentFuelStatus = FuelingStatus(
                                                                            currentAmount = currentFuelingStatus.fdCdata.deviceClass.currentAmount,
                                                                            currentVolume = currentFuelingStatus.fdCdata.deviceClass.currentVolume,
                                                                            currentUnitPrice = currentFuelingStatus.fdCdata.deviceClass.currentUnitPrice,
                                                                            currentNozzle = currentFuelingStatus.fdCdata.deviceClass.currentNozzle,
                                                                            releaseToken = currentFuelingStatus.fdCdata.deviceClass.releaseToken,
                                                                            authorisationApplicationSender = currentFuelingStatus.fdCdata.deviceClass.authorisationApplicationSender,
                                                                            postTransactionData = POSTransactionData(
                                                                                posTransactionData = currentFuelingStatus.fdCdata.deviceClass.posTransData.posTransactionData,
                                                                                tranType = currentFuelingStatus.fdCdata.deviceClass.posTransData.tranType,
                                                                                epsStan = currentFuelingStatus.fdCdata.deviceClass.posTransData.epsstan,
                                                                                merchandiseTrxAmount = currentFuelingStatus.fdCdata.deviceClass.posTransData.merchandiseTrxAmount,
                                                                            ),
                                                                        )
                                                                    )
                                                                }
                                                                fp
                                                            }
                                                    )
                                                }
                                        )
                                    }
                                }
                            }
                    }
                    launch {
                        getCurrentFuelingStatusUseCase.invoke(GetCurrentFuelingStatusRequestType().apply {
                            applicationSender = fdcConfig.applicationSender
                            workstationID = fdcConfig.workstationID
                            requestID = pumpRequestId
                            poSdata = GetCurrentFuelingStatusPOSdataType().apply {
                                posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                                deviceClass = DeviceClassType().apply {
                                    type = Type.FP
                                    deviceID = eachPump.deviceId
                                }
                            }
                        })
                    }
                }
        }
    }
}