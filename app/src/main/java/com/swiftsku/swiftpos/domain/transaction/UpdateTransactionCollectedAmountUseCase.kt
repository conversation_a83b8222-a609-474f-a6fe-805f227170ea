package com.swiftsku.swiftpos.domain.transaction


import com.swiftsku.swiftpos.data.model.TxnPayment
import com.swiftsku.swiftpos.data.model.paymentAmount
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class UpdateTransactionCollectedAmountUseCase @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {
    suspend operator fun invoke(
        transactionSummary: TransactionSummary,
        payments: Map<TxnPaymentType, TxnPayment>
    ): TransactionSummary = withContext(dispatcher) {
        var summary = transactionSummary
        payments.forEach { (type, payment) ->
            summary = when (type) {
                TxnPaymentType.Card -> summary.copy(cardAmountCollected = payment.paymentAmount)
                TxnPaymentType.Cash -> summary.copy(cashAmountCollected = payment.paymentAmount)
                TxnPaymentType.EBT -> summary.copy(ebtAmountCollected = payment.paymentAmount)
                TxnPaymentType.Cheque -> summary.copy(checkAmountCollected = payment.paymentAmount)
            }
        }
        summary
    }
}