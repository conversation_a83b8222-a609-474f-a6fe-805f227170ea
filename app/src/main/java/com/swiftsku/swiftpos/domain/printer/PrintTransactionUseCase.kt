package com.swiftsku.swiftpos.domain.printer

import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashDepositTxn
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.CashWithdrawalTxn
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.PayoutEvent
import com.swiftsku.swiftpos.data.model.ReceiptItem
import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.VoidTransaction
import com.swiftsku.swiftpos.data.model.coupon
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.data.type.PayoutType
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.LegacyPrinter
import com.swiftsku.swiftpos.domain.printer.dto.PrintBalanceInput
import com.swiftsku.swiftpos.domain.printer.dto.PrintTxnReceiptInput
import com.swiftsku.swiftpos.extension.formattedAmount
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.modules.printer.templates.transaction.BarcodeTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.CardTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.CartTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.CashAdjustmentTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.EbtTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.LoyaltyTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.PayoutTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.StoreTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.TransactionSummaryTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.TransactionTemplate
import com.swiftsku.swiftpos.modules.printer.templates.transaction.TransactionTemplatePrinter
import com.swiftsku.swiftpos.modules.printer.templates.transaction.TxnInfoTemplate
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineDispatcher
import java.util.Date
import javax.inject.Inject


class PrintTransactionUseCase @Inject constructor(
    @LegacyPrinter private val legacyPrinterManager: ISunMiPrinter,
    @IODispatcher private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(input: PrintTxnReceiptInput) {
        val storeConfig = input.storeConfig
        val receiptInfo = storeConfig.receiptInfo
        val loyaltyAccount = input.loyaltyAccount
        val cardInfo = input.cardInfo
        val change = input.change
        val ebtPayment = input.ebtPayment
        val ebtInfo = input.ebtInfo
        val totalQuantity = input.cartItems.sumOfFloats { it.quantity.toFloat() }.toInt()

        val promotions = input.cartItems.sumOfFloats {
            it.promotion?.sumOfFloats { promotion -> promotion.promotionAmount }
                ?: 0f
        } + input.cartItems.sumOfFloats {
            it.discount.loyalty.sumOfFloats { discount -> discount.amount.toFloat() }
        }

        var transactionTemplate = TransactionTemplate(
            storeTemplate = StoreTemplate(
                storeName = receiptInfo.storeName,
                streetAddress = receiptInfo.streetAddress,
                city = receiptInfo.city,
                state = receiptInfo.state,
                zipCode = receiptInfo.zipCode,
                phoneNumber = receiptInfo.phoneNumber,
                transactionId = input.txnId,
                date = input.txnStartTime.toDateTime(),
            ),
            loyaltyTemplate = if (loyaltyAccount == null) {
                null
            } else LoyaltyTemplate(accountId = loyaltyAccount),
            cartTemplate = CartTemplate(
                itemDetails = input.cartItems.map { cartItem ->
                    ReceiptItem(
                        description = cartItem.itemName(),
                        quantity = cartItem.quantity,
                        amount = cartItem.totalItemPrice().formattedAmount(),
                        promotions = cartItem.promotion ?: emptyList(),
                        discounts = cartItem.discount.loyalty,
                    )
                }
            ),
            transactionSummaryTemplate = TransactionSummaryTemplate(
                subTotal = input.subTotal,
                total = input.total,
                tax = input.tax,
                cash = 0f,
                ebt = 0f,
                card = 0f,
                changeGiven = change,
                check = 0f,
                coupon = input.coupon,
                promotions = promotions,
                totalQuantity = totalQuantity,
                appliedFees = input.appliedFees,
                lotteryPayout = input.lotteryPayout,
                lotteryPayouts = input.lotteryPayouts
            ),
            ebtTemplate = if (ebtPayment is EBTPayment) {
                EbtTemplate(
                    ebtType = "EBT - ${ebtInfo?.ebtType?.value}",
                    ebtPaidAmount = ebtPayment.amount,
                    ebtBalance = ebtInfo?.balanceInCents.toDollars(),
                    cardEntryType = ebtInfo?.cardEntryMode.orEmpty(),
                    refId = ebtInfo?.ref.orEmpty(),
                    authCode = ebtInfo?.auth.orEmpty(),
                    cardLast4 = ebtInfo?.cardNo.orEmpty()
                )
            } else null,
            barcodeTemplate = BarcodeTemplate(
                transactionId = input.txnId,
                printTxnId = input.storeConfig.printTxnIdBarcode
            )
        )
        if (input.cashPayment is CashPayment) {
            transactionTemplate = transactionTemplate.copy(
                transactionSummaryTemplate = transactionTemplate.transactionSummaryTemplate?.copy(
                    cash = input.cashPayment.tender
                )
            )
        }
        if (input.chequePayment is ChequePayment) {
            transactionTemplate = transactionTemplate.copy(
                transactionSummaryTemplate = transactionTemplate.transactionSummaryTemplate?.copy(
                    check = input.chequePayment.amount
                )
            )
        }

        if (input.cardPayment is CardPayment && cardInfo != null) {
            val amount = input.cardPayment.amount
            transactionTemplate = transactionTemplate.copy(
                cardDetail = CardTemplate(
                    merchantId = cardInfo.merchantId,
                    approvalCode = cardInfo.approvalCode,
                    terminalId = storeConfig.posNumber,
                    logo = cardInfo.logo,
                    transactionId = cardInfo.transactionId,
                    account = cardInfo.account,
                    entry = cardInfo.entry,
                    label = cardInfo.label.ifEmpty { "Card Details" },
                    amount = amount,
                ),
                transactionSummaryTemplate = transactionTemplate.transactionSummaryTemplate?.copy(
                    card = amount
                )
            )
        }

        if (input.ebtPayment is EBTPayment) {
            transactionTemplate = transactionTemplate.copy(
                transactionSummaryTemplate = transactionTemplate.transactionSummaryTemplate?.copy(
                    ebt = input.ebtPayment.amount
                ),
            )
        }

        if (input.isRefund) {
            transactionTemplate = transactionTemplate.copy(
                txnInfoTemplate = TxnInfoTemplate("REFUND RECEIPT")
            )
        }
        if (input.isVoid) {
            transactionTemplate = transactionTemplate.copy(
                txnInfoTemplate = TxnInfoTemplate("VOID TRANSACTION"),
                printFooter = false,
                barcodeTemplate = null
            )
        }
        input.payoutData?.let {
            val txnInfoLabel = when (it.category) {
                PayoutType.Lottery -> "LOTTERY PAYOUT"
                PayoutType.Vendor -> "VENDOR PAYOUT"
            }
            transactionTemplate = transactionTemplate.copy(
                txnInfoTemplate = TxnInfoTemplate(txnInfoLabel),
                payoutTemplate = PayoutTemplate(it),
                cartTemplate = null,
                transactionSummaryTemplate = null,
                barcodeTemplate = null
            )
        }
        input.cashWithdrawalData?.let {
            transactionTemplate = transactionTemplate.copy(
                txnInfoTemplate = TxnInfoTemplate("SAFE DROP"),
                cashAdjustmentTemplate = CashAdjustmentTemplate(
                    "Cash moved to safe", it.amount?.formattedAmount() ?: ""
                ),
                printFooter = false,
                cartTemplate = null,
                transactionSummaryTemplate = null,
                barcodeTemplate = null
            )
        }
        input.cashDepositData?.let {
            transactionTemplate = transactionTemplate.copy(
                txnInfoTemplate = TxnInfoTemplate("SAFE LOAN"),
                cashAdjustmentTemplate = CashAdjustmentTemplate(
                    "Cash taken from safe", it.amount?.formattedAmount() ?: ""
                ),
                printFooter = false,
                cartTemplate = null,
                transactionSummaryTemplate = null,
                barcodeTemplate = null
            )
        }

        try {

            val storeTemplate = TransactionTemplatePrinter(
                transactionTemplate = transactionTemplate,
                coroutineDispatcher = dispatcher
            )
            legacyPrinterManager.print(storeTemplate)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    suspend fun printEbtBalance(input: PrintBalanceInput) {
        val receiptInfo = input.storeConfig.receiptInfo
        val ebtInfo = input.ebtInfo
        val transactionTemplate = TransactionTemplate(
            storeTemplate = StoreTemplate(
                storeName = receiptInfo.storeName,
                streetAddress = receiptInfo.streetAddress,
                city = receiptInfo.city,
                state = receiptInfo.state,
                zipCode = receiptInfo.zipCode,
                phoneNumber = receiptInfo.phoneNumber,
                transactionId = input.txnId,
                date = Date().toDateTime(),
            ),
            ebtTemplate = EbtTemplate(
                ebtType = "EBT - ${ebtInfo.ebtType?.value}",
                ebtPaidAmount = 0f,
                ebtBalance = ebtInfo.balanceInCents.toDollars(),
                cardEntryType = ebtInfo.cardEntryMode.orEmpty(),
                refId = ebtInfo.ref.orEmpty(),
                authCode = ebtInfo.auth.orEmpty(),
                cardLast4 = ebtInfo.cardNo.orEmpty()
            )
        )
        try {
            val storeTemplate = TransactionTemplatePrinter(
                transactionTemplate = transactionTemplate,
                coroutineDispatcher = dispatcher
            )
            legacyPrinterManager.print(storeTemplate)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    fun getPrintTxnReceiptInput(
        storeConfig: StoreConfig,
        transaction: Transaction
    ): PrintTxnReceiptInput {
        val cashPayment = (transaction as? SaleTransaction)?.txnPayment?.get(TxnPaymentType.Cash)
        var cardPayment = (transaction as? SaleTransaction)?.txnPayment?.get(TxnPaymentType.Card)
        var ebtPayment = (transaction as? SaleTransaction)?.txnPayment?.get(TxnPaymentType.EBT)
        val chequePayment = (transaction as? SaleTransaction)?.txnPayment?.get(TxnPaymentType.Cheque)
        val coupon = (transaction as? SaleTransaction)?.coupon() ?: 0f
        val loyaltyId = (transaction as? SaleTransaction)?.accountInfo?.loyaltyId

        val total = when (transaction) {
            is SaleTransaction -> transaction.txnTotalGrandAmount - coupon
            is VoidTransaction -> transaction.txnTotalGrandAmount
            is RefundTransaction -> transaction.txnTotalGrandAmount
            is PayoutEvent -> transaction.payoutData.amount
            is CashWithdrawalTxn -> transaction.data.amount ?: 0f
            is CashDepositTxn -> transaction.data.amount ?: 0f
            else -> 0f
        }
        val subTotal = when (transaction) {
            is SaleTransaction -> transaction.txnTotalNetAmount
            is VoidTransaction -> transaction.txnTotalNetAmount
            is RefundTransaction -> transaction.txnTotalNetAmount
            is PayoutEvent -> transaction.payoutData.amount
            is CashWithdrawalTxn -> transaction.data.amount ?: 0f
            is CashDepositTxn -> transaction.data.amount ?: 0f
            else -> 0f
        }
        val tax = when (transaction) {
            is SaleTransaction -> transaction.txnTotalTaxNetAmount
            is VoidTransaction -> transaction.txnTotalTaxNetAmount
            is RefundTransaction -> transaction.txnTotalTaxNetAmount
            is PayoutEvent -> transaction.payoutData.amount
            is CashWithdrawalTxn -> transaction.data.amount ?: 0f
            is CashDepositTxn -> transaction.data.amount ?: 0f
            else -> 0f
        }
        // Remove Card or EBT detail depending on the refund state.
        if (transaction.txnType == TransactionType.SaleRefund || transaction is RefundTransaction) {
            val isCardRefunded = (transaction as? SaleTransaction)?.refundRes?.gen?.ok == true
            val isEbtRefunded = (transaction as? SaleTransaction)?.refundRes?.ebt?.ok == true
            if (!isCardRefunded) {
                cardPayment = null
            }
            if (!isEbtRefunded) {
                ebtPayment = null
            }
        }

        return PrintTxnReceiptInput(
            cardInfo = (transaction as? SaleTransaction)?.cardInfo,
            ebtInfo = (transaction as? SaleTransaction)?.ebtInfo,
            storeConfig = storeConfig,
            change = (cashPayment as? CashPayment)?.change ?: 0f,
            txnStartTime = transaction.txnStartTime,
            txnId = transaction.txnId,
            cartItems = transaction.txnItems.filter { it.status == TransactionItemStatus.Normal },
            coupon = coupon,
            subTotal = subTotal.coerceAtLeast(0f),
            total = total.coerceAtLeast(0f),
            tax = tax.coerceAtLeast(0f),
            cashPayment = cashPayment,
            cardPayment = cardPayment,
            ebtPayment = ebtPayment,
            chequePayment = chequePayment,
            loyaltyAccount = loyaltyId,
            lotteryPayout = (transaction as? SaleTransaction)?.lotteryPayout,
            lotteryPayouts = (transaction as? SaleTransaction)?.lotteryPayouts,
            appliedFees = (transaction as? SaleTransaction)?.appliedFees,
            isRefund = (transaction.txnType == TransactionType.SaleRefund || transaction.txnType == TransactionType.Refund),
            isVoid = (transaction is VoidTransaction),
            payoutData = (transaction as? PayoutEvent)?.payoutData,
            cashWithdrawalData = (transaction as? CashWithdrawalTxn)?.data,
            cashDepositData = (transaction as? CashDepositTxn)?.data
        )
    }

    suspend fun printIfAllowed(
        storeConfig: StoreConfig?,
        transaction: Transaction
    ) {
        if (storeConfig?.printAfterCreation?.contains(transaction.txnType) == true) {
            val printTxnReceiptInput = getPrintTxnReceiptInput(storeConfig, transaction)
            invoke(printTxnReceiptInput)
        }
    }
}