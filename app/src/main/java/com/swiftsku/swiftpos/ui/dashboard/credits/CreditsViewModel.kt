package com.swiftsku.swiftpos.ui.dashboard.credits

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.swiftsku.swiftpos.data.couchbase.credit.CreditRepository
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.CreditAccountDoc
import com.swiftsku.swiftpos.data.model.CreditStats
import com.swiftsku.swiftpos.data.model.LedgerDirection
import com.swiftsku.swiftpos.data.model.LedgerEntry
import com.swiftsku.swiftpos.data.model.LedgerReason
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundOption
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject
import kotlin.math.abs


@HiltViewModel
class CreditsViewModel @Inject constructor(
    private val creditRepository: CreditRepository
) : ViewModel() {

    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    val toastMessage: StateFlow<ToastMessage?> = _toastMessage
    private val _terminalConfig = MutableStateFlow<StoreConfig?>(null)
    private val _creditStats = MutableStateFlow<CreditStats?>(null)
    val creditStats = _creditStats.asStateFlow()
    private val _creditAccountsDoc = MutableStateFlow<CreditAccountDoc?>(null)
    val creditAccountsDoc = _creditAccountsDoc
    private val _selectedAccountId = MutableStateFlow<String?>(null)
    val selectedAccountId = _selectedAccountId
    private val _ledgerEntries = MutableStateFlow<Result<List<LedgerEntry>>?>(null)
    val ledgerEntries = _ledgerEntries

    init {
        creditRepository.createCollection()
    }

    fun updateTerminalConfig(terminalConfig: StoreConfig) = viewModelScope.launch {
        _terminalConfig.emit(terminalConfig)
        creditRepository.getCreditAccount(terminalConfig.storeCode).collectLatest {
            _creditAccountsDoc.emit(it)
        }
    }

    fun showToast(message: String, type: ToastMessageType) = viewModelScope.launch {
        _toastMessage.emit(
            ToastMessage(message = message, type = type)
        )
    }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }

    fun saveCreditAccount(creditAccountDoc: CreditAccountDoc) = viewModelScope.launch {
        if (creditRepository.saveCreditAccounts(creditAccountDoc)) {
            showToast("Credit accounts updated", ToastMessageType.Success)
        } else {
            showToast("Failed to update credit accounts", ToastMessageType.Success)
        }
    }

    fun setSelectedAccount(account: CreditAccount) = viewModelScope.launch {
        _selectedAccountId.emit(account.id)
        getLedgerEntries(account)
    }

    private fun getLedgerEntries(account: CreditAccount) = viewModelScope.launch {
        _ledgerEntries.emit(Result.Loading)
        creditRepository.getLedgerEntries(account.id).collectLatest {
            _ledgerEntries.emit(Result.Success(it))
        }
    }

    fun clearSelectedAccount() = viewModelScope.launch {
        _selectedAccountId.emit(null)
        _ledgerEntries.emit(null)
    }

    fun createFundEntry(
        fundOption: FundOption,
        amountCents: Int,
        storeCode: String,
        posNumber: String,
        cashierId: String,
        notes: String?
    ) = viewModelScope.launch {
        val fundMop = when (fundOption) {
            FundOption.CASH -> TxnPaymentType.Cash
            FundOption.CHECK -> TxnPaymentType.Cheque
            FundOption.CARD -> TxnPaymentType.Card
            else -> null
        }
        val ledgerReason = when (fundOption) {
            FundOption.CASH, FundOption.CARD, FundOption.CHECK -> LedgerReason.ADD_FUNDS
            FundOption.ADJUSTMENT -> LedgerReason.ADJUSTMENT
        }
        val ledgerDirection = when (fundOption) {
            FundOption.CASH, FundOption.CARD, FundOption.CHECK -> LedgerDirection.CREDIT
            FundOption.ADJUSTMENT -> if (amountCents > 0) LedgerDirection.CREDIT else LedgerDirection.DEBIT
        }
        _creditAccountsDoc.value?.let { creditAccountDoc ->
            val selectedId = _selectedAccountId.value
            val originalAccount = creditAccountDoc.creditAccounts[selectedId]

            if (originalAccount != null && selectedId != null) {
                val newOutstandingCents = originalAccount.currentOutstandingCents.plus(amountCents)
                val now = System.currentTimeMillis()

                val updatedAccount = originalAccount.copy(
                    currentOutstandingCents = newOutstandingCents,
                    updatedAt = now
                )

                val updatedAccounts = creditAccountDoc.creditAccounts.toMutableMap()
                updatedAccounts[selectedId] = updatedAccount

                val updatedCreditAccountDoc =
                    creditAccountDoc.copy(creditAccounts = updatedAccounts)

                val txnId = generateTransactionId(storeCode = storeCode, posNumber = posNumber)

                val ledgerEntry = LedgerEntry(
                    txnId = txnId,
                    accountId = updatedAccount.id,
                    linkedTxnId = txnId,
                    txnTime = System.currentTimeMillis(),
                    cashierId = cashierId,
                    ledgerReason = ledgerReason,
                    ledgerDirection = ledgerDirection,
                    fundMop = fundMop,
                    notes = notes,
                    amountCents = abs(amountCents),
                    newAccountOutstandingCents = newOutstandingCents,
                    storeCode = storeCode
                )

                val saved = creditRepository.createLedgerEntry(
                    creditAccount = updatedAccount,
                    creditAccountDoc = updatedCreditAccountDoc,
                    ledgerEntry = ledgerEntry
                )
                if (saved) {
                    showToast("Saved successfully", ToastMessageType.Success)
                }
            }
        }
    }
}