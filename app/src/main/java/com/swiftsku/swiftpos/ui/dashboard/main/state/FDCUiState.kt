package com.swiftsku.swiftpos.ui.dashboard.main.state

import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.swiftsku.swiftpos.data.model.FuelSaleTrx
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.serialization.Serializable

data class FDCUiState(
    val selectedPump: FuelPumpState? = null,
    val showFuelDialog: Boolean = false,
    val showFuelTransactionDialog: Boolean = false,
    val showProductsDialog: Boolean = false,
    val transactions: List<SaleTransaction> = emptyList(),
    val autoCompleteCashTransaction: Boolean = false,
    val pendingFuelTxns: List<SaleTransaction> = emptyList(),
    val unpaidFuelTxns: List<SaleTransaction> = emptyList(),
)

fun FDCUiState.showFuelDialog() = this.selectedPump != null && this.showFuelDialog
fun FDCUiState.showFuelTransactionDialog() =
    this.selectedPump != null && this.showFuelTransactionDialog

fun FDCUiState.shouldShowProductsDialog() = this.selectedPump != null && this.showProductsDialog
fun FDCUiState.closeProductsDialog() = this.copy(showProductsDialog = false)

fun FDCUiState.closeFPDialog() = this.copy(showFuelDialog = false)

fun FDCUiState.closeFPTransactionDialog() = this.copy(showFuelTransactionDialog = false)
fun FDCUiState.autoCompleteCashTransaction(complete: Boolean) =
    this.copy(autoCompleteCashTransaction = complete)

fun FDCUiState.openFPDialog(
    selectedPump: FuelPumpState
) =
    this.copy(
        showFuelDialog = true,
        selectedPump = selectedPump,
        showFuelTransactionDialog = false
    )

fun FDCUiState.openFPTransactionDialog(
    selectedPump: FuelPumpState
) =
    this.copy(
        showFuelDialog = false,
        showFuelTransactionDialog = true,
        selectedPump = selectedPump
    )


data class FPLockState(
    val lockedAmount: Double = 0.0,
    val lockedVolume: Double = 0.0,
    val lockedPrice: Double? = null,
    val lockedPumpState: FuelPumpState,
    val transactionItemId: String
)

data class PosTxnData(
    val txnId: String,
    val txnItemId: String,
)

fun posTxnDataToFdc(posTxnData: PosTxnData): String {
    /**
     * Sending long string like this "1ff6e-8-731b488, 8427f941-a760-4574-a6f6-78a2f8b1a604",
     * is crashing the xml-pilot, for now we can just send the last part of the string
     * i.e. "731b488,78a2f8b1a604"
     */
    return "${posTxnData.txnId.split("-").last()},${posTxnData.txnItemId.split("-").last()}"
}

fun parsePosDataFromFdc(storeCode: String, posNumber: String, data: String?): PosTxnData? {
    data?.let {
        return try {
            val txnId = "$storeCode-$posNumber-${data.split(",")[0].trim()}"
            val txnItemIdLastPart = data.split(",")[1].trim()
            PosTxnData(txnId, txnItemIdLastPart)
        } catch (ex: Exception) {
            EventUtils.recordException(ex)
            null
        }
    }
    return null
}

val FPLockState.id get() = "${this.lockedPumpState.pumpNo}-${this.lockedPumpState.deviceId}"

val FPLockState.isApproved: Boolean
    get() = this.lockedAmount == 0.0 && this.lockedVolume == 0.0