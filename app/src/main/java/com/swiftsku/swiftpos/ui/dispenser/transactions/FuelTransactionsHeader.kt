package com.swiftsku.swiftpos.ui.dispenser.transactions

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun FuelTransactionsHeader() {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(top = 16.dp, bottom = 16.dp, start = 20.dp, end = 20.dp)
    ) {
        Text(
            text = "Txn ID",
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = "Date",
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite
        )
        Text(
            text = "Sale From",
            modifier = Modifier.weight(1f).padding(end = 32.dp),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = "Payment",
            modifier = Modifier.weight(1f).padding(end = 32.dp),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = "Volume(gal)",
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.Center
        )
        Text(
            text = "Fuel Amount",
            modifier = Modifier.weight(1f).padding(end = 32.dp),
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.blackAndWhite,
            textAlign = TextAlign.End
        )
        Box(modifier = Modifier.weight(1.2f)) {}
        Box(modifier = Modifier.weight(1f)) {}
    }
}