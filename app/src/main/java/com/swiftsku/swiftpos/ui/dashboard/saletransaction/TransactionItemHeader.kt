package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun TransactionItemHeader(
    totalQuantity: Int = 0,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp)
    ) {
        Text(text = "Description", modifier = Modifier.weight(3f))
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(text = "Qty ($totalQuantity)", modifier = Modifier.weight(1f))
            Text(text = "Amount")
        }
    }
}