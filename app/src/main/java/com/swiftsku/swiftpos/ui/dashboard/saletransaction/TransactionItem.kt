package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DoNotDisturbOn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.totalAmount
import com.swiftsku.swiftpos.extension.formattedAmount
import com.swiftsku.swiftpos.ui.theme.blackAndWhite

@Composable
fun TransactionItem(
    description: String = "",
    qty: Int = 0,
    amount: Float = 0f,
    deleted: Boolean = false
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp)
    ) {
        Row(
            modifier = Modifier.weight(3f)
        ) {
            if (deleted) {
                Icon(
                    imageVector = Icons.Default.DoNotDisturbOn,
                    contentDescription = "Deleted",
                    tint = MaterialTheme.colors.error
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = description,
                color = MaterialTheme.colors.blackAndWhite,
                style = MaterialTheme.typography.body2
            )
        }
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = "$qty",
                color = MaterialTheme.colors.blackAndWhite,
                style = MaterialTheme.typography.body2
            )
            Text(
                text = amount.formattedAmount(),
                color = MaterialTheme.colors.blackAndWhite,
                style = MaterialTheme.typography.body2
            )
        }
    }
}