package com.swiftsku.swiftpos.ui.presentation

import android.annotation.SuppressLint
import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.cart.CartSummaryItem
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.grandTotal
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

data class LoyaltyInput(
    val number: String,
    val inputTs: Long,
)

class CustomerPresentation(
    outerContext: Context?,
    display: Display?
) : Presentation(outerContext, display), View.OnClickListener {


    private lateinit var rvCart: RecyclerView
    private lateinit var rvOffers: RecyclerView
    private lateinit var tvSubTotal: TextView
    private lateinit var tvTax: TextView
    private lateinit var tvGrandTotal: TextView
    private lateinit var tvAmountCollected: TextView
    private lateinit var tvPromotion: TextView
    private lateinit var tvCoupon: TextView
    private lateinit var tvLottery: TextView
    private lateinit var couponLayout: View
    private lateinit var lotteryLayout: View
    private lateinit var etPhone: EditText
    private lateinit var tvQty: TextView
    private lateinit var llRoot: LinearLayout
    private lateinit var llPromotion: LinearLayout
    private lateinit var llAmountCollected: LinearLayout
    private lateinit var llCardFee: LinearLayout
    private lateinit var llCardTotal: LinearLayout
    private lateinit var tvCardFeeTitle: TextView
    private lateinit var tvCardFeeValue: TextView
    private lateinit var tvCardTotalValue: TextView
    private lateinit var tvNoOffers: TextView
    private lateinit var llCash: LinearLayout
    private lateinit var llCard: LinearLayout
    private lateinit var tvCashAmount: TextView
    private lateinit var tvCardAmount: TextView

    private lateinit var phonePad: ConstraintLayout

    private lateinit var bt0: Button
    private lateinit var bt1: Button
    private lateinit var bt2: Button
    private lateinit var bt3: Button
    private lateinit var bt4: Button
    private lateinit var bt5: Button
    private lateinit var bt6: Button
    private lateinit var bt7: Button
    private lateinit var bt8: Button
    private lateinit var bt9: Button
    private lateinit var btOk: Button
    private lateinit var btClear: Button
    private lateinit var btBack: Button

    private val listAdapter = CartListAdapter(mutableListOf())
    private val offersAdapter = OffersAdapter(mutableListOf())

    private val _phoneNumber = MutableStateFlow<LoyaltyInput?>(null)
    val phoneNumber = _phoneNumber

    private val scope = CoroutineScope(Dispatchers.Main)


    private fun initViews() {
        llRoot = findViewById(R.id.linearLayout)
        tvSubTotal = findViewById(R.id.tvSubTotalValue)
        tvTax = findViewById(R.id.tvTaxValue)
        tvGrandTotal = findViewById(R.id.tvTotalValue)
        tvAmountCollected = findViewById(R.id.tvAmountCollectedValue)
        tvPromotion = findViewById(R.id.tvPromotionValue)
        tvCoupon = findViewById(R.id.tvCouponValue)
        tvLottery = findViewById(R.id.tvLotteryValue)
        couponLayout = findViewById(R.id.llCoupon)
        lotteryLayout = findViewById(R.id.llLottery)
        llPromotion = findViewById(R.id.llPromotion)
        llAmountCollected = findViewById(R.id.llAmountCollected)
        llCardFee = findViewById(R.id.llCardFee)
        llCardTotal = findViewById(R.id.llCardTotal)
        tvCardFeeTitle = findViewById(R.id.tvCardFeeTitle)
        tvCardFeeValue = findViewById(R.id.tvCardFeeValue)
        tvCardTotalValue = findViewById(R.id.tvCardTotalValue)
        tvQty = findViewById(R.id.tvQty)
        tvNoOffers = findViewById(R.id.tvNoOffers)
        llCash = findViewById(R.id.llCash)
        llCard = findViewById(R.id.llCard)
        tvCashAmount = findViewById(R.id.tvCashAmount)
        tvCardAmount = findViewById(R.id.tvCardAmount)

        bt0 = findViewById(R.id.bt0)
        bt1 = findViewById(R.id.bt1)
        bt2 = findViewById(R.id.bt2)
        bt3 = findViewById(R.id.bt3)
        bt4 = findViewById(R.id.bt4)
        bt5 = findViewById(R.id.bt5)
        bt6 = findViewById(R.id.bt6)
        bt7 = findViewById(R.id.bt7)
        bt8 = findViewById(R.id.bt8)
        bt9 = findViewById(R.id.bt9)
        btOk = findViewById(R.id.btOk)
        etPhone = findViewById(R.id.etPhone)
        phonePad = findViewById(R.id.phonePad)
        btClear = findViewById(R.id.btClear)
        btBack = findViewById(R.id.btBack)

        btBack.setOnClickListener(this)
        btClear.setOnClickListener(this)
        bt0.setOnClickListener(this)
        bt1.setOnClickListener(this)
        bt2.setOnClickListener(this)
        bt3.setOnClickListener(this)
        bt4.setOnClickListener(this)
        bt5.setOnClickListener(this)
        bt6.setOnClickListener(this)
        bt7.setOnClickListener(this)
        bt8.setOnClickListener(this)
        bt9.setOnClickListener(this)
        btOk.setOnClickListener(this)


        // cart
        rvCart = findViewById(R.id.rvCart)
        rvCart.layoutManager = LinearLayoutManager(this.context)
        rvCart.adapter = listAdapter

        rvOffers = findViewById(R.id.rvOffers)
        rvOffers.layoutManager = LinearLayoutManager(this.context)
        rvOffers.adapter = offersAdapter

        updateCartSummary(transactionSummary = TransactionSummary())
    }

    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.secondary_display)
        initViews()
    }

    @SuppressLint("SetTextI18n")
    fun updateCartSummary(transactionSummary: TransactionSummary) {
        tvSubTotal.text = transactionSummary.transactionTotalNetAmount.toDollars()
        tvTax.text = transactionSummary.transactionTotalTaxNetAmount.toDollars()
        tvGrandTotal.text = transactionSummary.grandTotal().toDollars()
        updatePromotion(transactionSummary.promotionApplied)
        updateAmountCollected(transactionSummary.totalAmountCollected())
        updateCoupon(transactionSummary.couponAmount)
        updateLottery(transactionSummary.lotteryAmount)
        val cardFee = transactionSummary.calculatedFees.find { it.type == FeeType.CARD_PROCESSING }
        updateCardFee(transactionSummary.pendingAmount(), cardFee)
    }

    fun showDisplay(show: Boolean) {
        llRoot.visibility = if (show) View.VISIBLE else View.GONE
    }

    @SuppressLint("SetTextI18n")
    private fun updatePromotion(amount: Float) {
        tvPromotion.text = (amount * -1).toDollars()
        if (amount > 0) {
            llPromotion.visibility = View.VISIBLE
        } else {
            llPromotion.visibility = View.GONE
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateAmountCollected(amount: Float) {
        tvAmountCollected.text = amount.toDollars()

        if (amount > 0) {
            llAmountCollected.visibility = View.VISIBLE
        } else {
            llAmountCollected.visibility = View.GONE
        }

    }

    @SuppressLint("SetTextI18n")
    fun updateCartItems(items: List<TransactionItem>) {
        listAdapter.refresh(items)
        tvQty.text = "QTY (${items.sumOf { it.quantity }})"
        if (items.isEmpty()) {
            resetTransaction()
        }

    }

    @SuppressLint("SetTextI18n")
    private fun updateCoupon(coupon: Float) {
        if (coupon > 0) {
            tvCoupon.text = (coupon * -1).toDollars()
            couponLayout.visibility = View.VISIBLE
        } else {
            couponLayout.visibility = View.GONE
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateLottery(lottery: Float) {
        if (lottery > 0) {
            tvLottery.text = (lottery * -1).toDollars()
            lotteryLayout.visibility = View.VISIBLE
        } else {
            lotteryLayout.visibility = View.GONE
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateCardFee(pendingAmount: Float, cardFee: AppliedFee?) {
        cardFee?.let {
            llCardFee.visibility = View.VISIBLE
            llCardTotal.visibility = View.VISIBLE
            llCash.visibility = View.VISIBLE
            llCard.visibility = View.VISIBLE
            tvCardFeeTitle.text = "Card Fee(${it.info})"
            tvCardFeeValue.text = it.amount.toDollars()
            tvCardTotalValue.text = pendingAmount.plus(it.amount).toDollars()
            tvCashAmount.text = pendingAmount.toDollars()
            tvCardAmount.text = pendingAmount.plus(it.amount).toDollars()
        } ?: run {
            llCardFee.visibility = View.GONE
            llCardTotal.visibility = View.GONE
            llCash.visibility = View.GONE
            llCard.visibility = View.GONE
        }
    }

    fun getUserPhoneNumber() {
        // show phone number input

        phonePad.visibility = View.VISIBLE
    }

    private fun resetTransaction() {
        // reset transaction
        etPhone.setText("")
        phonePad.visibility = View.GONE
    }

    fun dismissUserInput() {
        resetTransaction()
    }

    override fun onClick(p0: View?) {
        when (p0?.id) {
            R.id.bt0 -> {
                etPhone.append("0")
            }

            R.id.bt1 -> {
                etPhone.append("1")
            }

            R.id.bt2 -> {
                etPhone.append("2")
            }

            R.id.bt3 -> {
                etPhone.append("3")
            }

            R.id.bt4 -> {
                etPhone.append("4")
            }

            R.id.bt5 -> {
                etPhone.append("5")
            }

            R.id.bt6 -> {
                etPhone.append("6")
            }

            R.id.bt7 -> {
                etPhone.append("7")
            }

            R.id.bt8 -> {
                etPhone.append("8")
            }

            R.id.bt9 -> {
                etPhone.append("9")
            }

            R.id.btBack -> {
                val text = etPhone.text
                if (text.isNotEmpty()) {
                    val length = text.length
                    etPhone.setText(text.subSequence(0, length - 1))
                }

            }

            R.id.btClear -> {
                etPhone.setText("")
            }

            R.id.btOk -> {
                val text = etPhone.text
                if (text.isNotEmpty() && text.length >= 10 && text.length <= 11) {
                    scope.launch {
                        _phoneNumber.emit(LoyaltyInput(text.toString(), System.currentTimeMillis()))
                    }
                } else {
                    Toast.makeText(this.context, "Invalid Phone Number", Toast.LENGTH_SHORT).show()
                }
            }
        }

    }

    override fun onDetachedFromWindow() {
        scope.cancel()
        super.onDetachedFromWindow()
    }

    fun updateOffers(availableOffers: List<String>) {
        offersAdapter.updateOffers(availableOffers)
        if (availableOffers.isNotEmpty()) {
            tvNoOffers.visibility = View.GONE
        } else {
            tvNoOffers.visibility = View.VISIBLE
        }
    }
}