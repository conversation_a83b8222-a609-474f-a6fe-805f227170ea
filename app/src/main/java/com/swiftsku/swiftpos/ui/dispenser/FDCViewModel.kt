package com.swiftsku.swiftpos.ui.dispenser

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fdc.core.types.AuthorizeFuelPointPOSdataType
import com.fdc.core.types.AuthorizeFuelPointRequestType
import com.fdc.core.types.AuthorizeRequestDeviceClassType
import com.fdc.core.types.DeviceClassType
import com.fdc.core.types.DeviceState
import com.fdc.core.types.FQMMessage
import com.fdc.core.types.FQMMessageType
import com.fdc.core.types.FreeFuelPointRequestType
import com.fdc.core.types.LogOffRequestType
import com.fdc.core.types.OverallResult
import com.fdc.core.types.POSTransDataType
import com.fdc.core.types.POSdataBaseType
import com.fdc.core.types.POSdataBaseWithoutDeviceClassType
import com.fdc.core.types.ReserveFuelPointRequestType
import com.fdc.core.types.TerminateFuelPointRequestType
import com.fdc.core.types.TranTypeEnum
import com.fdc.core.types.TrueFalse
import com.fdc.core.types.Type
import com.orhanobut.logger.Logger
import com.swiftsku.fdc.core.di.usecases.core.FQMMessageListenerUseCase
import com.swiftsku.fdc.core.di.usecases.core.LogOffUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.AuthorizeFuelPointRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ReserveFuelPointRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.TerminateFPRequestUseCase
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FuelMeta
import com.swiftsku.swiftpos.data.model.PreFuel
import com.swiftsku.swiftpos.data.model.PumpMeta
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.coupon
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.provider.DashboardStateProvider
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.domain.dispenser.MovePrepayUseCase
import com.swiftsku.swiftpos.domain.dispenser.SetupFDCLogOnUseCase
import com.swiftsku.swiftpos.domain.dispenser.StopFuelPumpUseCase
import com.swiftsku.swiftpos.domain.dispenser.UpdateDSPStateWithFPStateUseCase
import com.swiftsku.swiftpos.domain.dispenser.WatchPumpUseCase
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.domain.printer.dto.PrintTxnReceiptInput
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCUiState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDC_MAX_PREPAY
import com.swiftsku.swiftpos.ui.dashboard.main.state.FPLockState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.PosTxnData
import com.swiftsku.swiftpos.ui.dashboard.main.state.ProductState
import com.swiftsku.swiftpos.ui.dashboard.main.state.addFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.areAllPumpsReady
import com.swiftsku.swiftpos.ui.dashboard.main.state.closeFPDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.closeFPTransactionDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.closeProductsDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.deletedFpLock
import com.swiftsku.swiftpos.ui.dashboard.main.state.isApproved
import com.swiftsku.swiftpos.ui.dashboard.main.state.isFueling
import com.swiftsku.swiftpos.ui.dashboard.main.state.openFPDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.openFPTransactionDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.posTxnDataToFdc
import com.swiftsku.swiftpos.ui.dashboard.main.state.pumps
import com.swiftsku.swiftpos.ui.dashboard.main.state.selectedPump
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.FUEL_CARD_MODE
import com.swiftsku.swiftpos.utils.FUEL_CASH_MODE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.util.Date
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import kotlin.math.max

@HiltViewModel
class FDCViewModel @Inject constructor(
    private val logOutUseCase: LogOffUseCase,
    private val reserveFP: ReserveFuelPointRequestUseCase,
    private val authorizeFP: AuthorizeFuelPointRequestUseCase,
    private val terminateFP: TerminateFPRequestUseCase,
    private val setUpFDC: SetupFDCLogOnUseCase,
    private val watchPumpUseCase: WatchPumpUseCase,
    private val freeFP: FreeFuelPointUseCase,
    private val transactionRepository: TransactionRepository,
    private val printTransactionUseCase: PrintTransactionUseCase,
    private val listenForFQMMessage: FQMMessageListenerUseCase,
    private val movePrepay: MovePrepayUseCase,
    private val dashboardStateProvider: DashboardStateProvider,
    private val stopFuelPump: StopFuelPumpUseCase,
    private val userDataSource: UserDataSource,
    private val updateFPState: UpdateDSPStateWithFPStateUseCase,
) : ViewModel() {

    private val _fdcState = MutableStateFlow(FDCState())
    val fdcState = _fdcState

    private val _uiState = MutableStateFlow(FDCUiState())
    val uiState: StateFlow<FDCUiState> = _uiState
    private val _fqmState = MutableStateFlow(FQMMessage())

    val fqmState: StateFlow<FQMMessage> = _fqmState.asStateFlow()

    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    val toastMessage: StateFlow<ToastMessage?> = _toastMessage

    private val _freeFPState = MutableStateFlow<TransactionItem?>(null)
    val freeFPState: StateFlow<TransactionItem?> = _freeFPState

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)
    private val _storeLevelConfig = MutableStateFlow<StoreUsers?>(null)
    private val _fdcConfig = MutableStateFlow<FDCConfig?>(null)

    private var fdcInitialized = AtomicBoolean(false)
    private var selectedFuelProduct: ProductState? = null


    init {
        setUpFQMListener()
    }

    private fun setUpFQMListener() = viewModelScope.launch {
        combine(
            _storeConfig,
            listenForFQMMessage.fqmMessage,
            _fdcConfig
        ) { storeConfig, fqmMessage, fdcConfig ->
            Triple(storeConfig, fqmMessage, fdcConfig)
        }.collectLatest {
            val (storeConfig, fqmMessage, fdcConfig) = it
            val fqmMessageType = fqmMessage.messageType
            _fqmState.emit(fqmMessage)
            if (fqmMessageType != null) {
                when (fqmMessageType) {
                    FQMMessageType.FDC_CONNECTION_REFUSED -> {}
                    FQMMessageType.FDC_CONNECTION_BROKEN -> resetState()
                    FQMMessageType.FDC_CONNECTED -> {
                        if (fdcConfig != null && storeConfig != null && !fdcInitialized.get()) {
                            fdcInitialized.set(true)
                            setupPendingTxnListener()
                            setUpFDC.invoke(
                                _fdcState,
                                viewModelScope,
                                storeConfig,
                                fdcConfig = fdcConfig,
                                cashierId = cashierId()
                            )
                        }
                    }
                }
            }
        }
    }

    private suspend fun cashierId() = userDataSource.getCurrentUser()?.username ?: "cashierId"

    private fun setupPendingTxnListener() = viewModelScope.launch {
        transactionRepository.getPendingFuelTransactionsFlow().collect { txns ->
            val unpaidFuelTxns = txns.filter { it.txnStatus == TransactionStatus.FuelDispensed }
            _uiState.update { it.copy(pendingFuelTxns = txns, unpaidFuelTxns = unpaidFuelTxns) }
        }
    }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }

    private fun authorizePump(
        deviceId: Int,
        trnType: TranTypeEnum = TranTypeEnum.PREPAY,
        fpLock: FPLockState,
        fpFuelMode: Int = FUEL_CASH_MODE,
        txnId: String,
        txnItemId: String
    ) = viewModelScope.launch {

        val fdcConfig = _fdcConfig.value ?: return@launch

        val requestId = generatePumpRequestId(fpLock.lockedPumpState.pumpNo)

        launch {
            authorizeFP.authorizeFuelResponse.collectLatest {}
        }
        launch {
            val posData = AuthorizeRequestDeviceClassType().apply {
                lockFuelSaleTrx = TrueFalse.TRUE
                type = Type.FP
                deviceID = deviceId
                posTransData = POSTransDataType().apply {
                    tranType = trnType
                    epsstan = 1.toBigInteger()
                    fuelMode = AuthorizeRequestDeviceClassType.FuelMode().apply {
                        modeNo = fpFuelMode
                    }
                    posTransactionData = posTxnDataToFdc(PosTxnData(txnId, txnItemId))
                }
            }

            val storeLevelConfig = _storeLevelConfig.value
            val maxPostPayAmount: Double = if (storeLevelConfig?.maxFuelPostpayLimitCents != null)
                storeLevelConfig.maxFuelPostpayLimitCents / 100.0
            else
                FDC_MAX_PREPAY

            if (fpLock.lockedVolume > 0) {
                posData.maxTrxVolume = fpLock.lockedVolume.toBigDecimal()
            } else {
                if (fpLock.lockedAmount > 0) {
                    posData.maxTrxAmount = fpLock.lockedAmount.toBigDecimal()
                } else {
                    posData.maxTrxAmount = maxPostPayAmount.toBigDecimal()
                }
            }

            authorizeFP(
                AuthorizeFuelPointRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = requestId
                    poSdata = AuthorizeFuelPointPOSdataType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = posData
                    }
                }
            )
        }
    }

    fun authorizePrepayPump(txn: Transaction, paymentMode: TxnPaymentType) =
        viewModelScope.launch {
            val pumpDeviceIds = txn.txnItems.filterIsInstance<TransactionItemWithFuel>()
                .map { it.preFuel.deviceId }
            val fpLocks = _fdcState.value.fpLock
                .filter { pumpDeviceIds.contains(it.lockedPumpState.deviceId) }
            fpLocks.forEach { lockedPump ->
                authorizePump(
                    deviceId = lockedPump.lockedPumpState.deviceId,
                    fpLock = lockedPump,
                    trnType = if (lockedPump.isApproved) TranTypeEnum.POSTPAY else TranTypeEnum.PREPAY,
                    txnId = txn.txnId,
                    txnItemId = lockedPump.transactionItemId,
                    fpFuelMode = if (paymentMode == TxnPaymentType.Card) FUEL_CARD_MODE else FUEL_CASH_MODE
                )
            }
        }


    override fun onCleared() {
        viewModelScope.launch {
            val fdcConfig = _fdcConfig.value ?: return@launch

            val pumpRequestId = generatePumpRequestId(9)
            logOutUseCase(LogOffRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = POSdataBaseWithoutDeviceClassType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                }
            })
        }
        super.onCleared()
    }

    val onPumpClick: (selectedPump: FuelPumpState) -> Unit = { selectedPump ->
        viewModelScope.launch {
            if (selectedPump.fpState?.fpState?.deviceState == null) {
                _fdcConfig.value?.let {
                    updateFPState.getFpState(selectedPump.deviceId, _fdcState, it)
                }
            } else {
                _uiState.update { state -> state.openFPDialog(selectedPump) }
            }
        }
    }

    fun onFuelPumpDialogDismiss(keepSelectedState: Boolean = true) {
        _uiState.update { state ->
            state.copy(
                selectedPump = if (keepSelectedState) state.selectedPump else null,
                showFuelDialog = !keepSelectedState
            )
        }
    }

    fun onFPTransactionDialogDismiss() {
        _uiState.update { state -> state.closeFPTransactionDialog() }
    }

    fun onFuelPresetClick() {
        _uiState.update { state -> state.copy(showProductsDialog = true, showFuelDialog = false) }
    }

    fun onProductDialogDismiss() {
        _uiState.update { state -> state.closeProductsDialog() }
    }

    fun onProductSelect(product: ProductState) {
        // Store selected product in memory
        selectedFuelProduct = product
        _uiState.update { state -> state.closeProductsDialog() }
    }

    fun onRestInGasClick(state: FuelPumpState, amount: Float) = viewModelScope.launch {
        onPrepayClickAmount(amount.toDouble(), state)
    }

    private fun resetState() = viewModelScope.launch {
        _uiState.update { FDCUiState() }
        _fdcState.emit(FDCState())
        fdcInitialized.set(false)
    }

    private suspend fun addFuelToCart(fpLock: FPLockState) {
        _fdcState.update { it.addFpLock(fpLock) }
        dashboardStateProvider.addTxnItem(
            TransactionItemWithFuel(
                transactionItemId = fpLock.transactionItemId,
                preFuel = PreFuel(
                    pumpNo = fpLock.lockedPumpState.pumpNo,
                    amount = fpLock.lockedAmount,
                    deviceId = fpLock.lockedPumpState.deviceId
                )
            )
        )
    }

    fun onPrepayClickAmount(amount: Double, state: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch

        val requestId = generatePumpRequestId(state.deviceId)

        launch {
            reserveFP.reserveFPResponse.collectLatest { response ->

                Logger.d("reserveFPResponse ${response.requestID}")

                if (response.overallResult == OverallResult.SUCCESS) {
                    if (response.isForCurrentDevice(
                            fdcConfig.workstationID,
                            requestId
                        )
                    ) {
                        addFuelToCart(
                            FPLockState(
                                lockedAmount = amount.to2Decimal(),
                                lockedPumpState = state,
                                transactionItemId = UUID.randomUUID().toString()
                            )
                        )
                        _uiState.update { it.closeFPDialog() }
                    }
                } else {
                    _toastMessage.emit(ToastMessage("${response.overallResult.value()} : Failed to reserve fuel"))
                }
            }
        }


        reserveFP(
            ReserveFuelPointRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = requestId
                poSdata = POSdataBaseType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = DeviceClassType().apply {
                        type = Type.FP
                        deviceID = state.deviceId
                    }
                }
            }
        )
    }

    fun onMovePrepayClick(state: FuelPumpState, newPumpNo: Int) = viewModelScope.launch {
        // open num pad dialog and accept the other fuel pump number
        val newPump = _fdcState.value.selectedPump(newPumpNo, newPumpNo) ?: return@launch
        val fdcConfig = _fdcConfig.value ?: return@launch
        movePrepay.invoke(state, newPump, _fdcState, fdcConfig, _storeLevelConfig.value)
    }

    fun onWatchPumpClick(state: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch

        _uiState.update { it.closeFPDialog() }
        if (state.isFueling) {
            watchPumpUseCase(state.deviceId, _fdcState, fdcConfig)
        }
    }

    fun isWatchingPump(deviceId: Int) = watchPumpUseCase.isWatching(deviceId)

    fun stopWatchingPump(deviceId: Int) {
        watchPumpUseCase.stopWatchingPump(deviceId)
    }

    fun onReprintClick(state: FuelPumpState) = viewModelScope.launch {
        try {
            transactionRepository
                .getFuelTransactions(state.deviceId)
                .filter { it.txnStatus == TransactionStatus.Complete }
                .maxByOrNull { it.txnStartTime }
                ?.let { reprintSaleInvoice(it.txnId) }
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    fun reprintSaleInvoice(saleTxnId: String) = viewModelScope.launch {
        val storeConfig = _storeConfig.value ?: return@launch

        val transaction = transactionRepository.getTransaction(saleTxnId)
        if (transaction is SaleTransaction) {
            val cashPayment = (transaction.txnPayment[TxnPaymentType.Cash])
            val coupon = transaction.coupon()
            val total = max(transaction.txnTotalGrandAmount - coupon, 0f)
            val change = (cashPayment as? CashPayment)?.change ?: 0f
            printTransactionUseCase(
                PrintTxnReceiptInput(
                    cardInfo = transaction.cardInfo,
                    storeConfig = storeConfig,
                    change = change,
                    txnStartTime = transaction.txnStartTime,
                    txnEndTime = transaction.txnEndTime,
                    txnId = transaction.txnId,
                    cartItems = transaction.txnItems.filter { it.status == TransactionItemStatus.Normal },
                    coupon = coupon,
                    subTotal = transaction.txnTotalNetAmount,
                    total = total,
                    tax = transaction.txnTotalTaxNetAmount,
                    cashPayment = cashPayment,
                    cardPayment = (transaction.txnPayment[TxnPaymentType.Card]),
                    ebtPayment = (transaction.txnPayment[TxnPaymentType.EBT]),
                    chequePayment = (transaction.txnPayment[TxnPaymentType.Cheque]),
                    creditPayment = (transaction.txnPayment[TxnPaymentType.Credit]),
                    loyaltyAccount = transaction.accountInfo?.loyaltyId,
                    lotteryPayout = transaction.lotteryPayout,
                    lotteryPayouts = transaction.lotteryPayouts,
                    appliedFees = transaction.appliedFees
                )
            )
        }
    }

    fun onOpenPumpClick(state: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch
        stopFuelPump.openPump(fdcConfig, state.pumpNo) { result ->
            viewModelScope.launch {
                _toastMessage.emit(ToastMessage("${result.value()} : Failed to resume pump"))
            }
        }
    }

    fun onStopPumpClick(state: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch
        stopFuelPump.closePump(fdcConfig, state.pumpNo) { result ->
            viewModelScope.launch {
                _toastMessage.emit(ToastMessage("${result.value()} : Failed to stop pump"))
            }
        }
    }

    fun onApprovePumpClick(state: FuelPumpState) = viewModelScope.launch {
        val fpLock = FPLockState(
            lockedPumpState = state,
            transactionItemId = UUID.randomUUID().toString()
        )
        _fdcState.update { it.addFpLock(fpLock) }
        val fuelTxnItem = TransactionItemWithFuel(
            transactionItemId = fpLock.transactionItemId,
            preFuel = PreFuel(
                pumpNo = fpLock.lockedPumpState.pumpNo,
                amount = fpLock.lockedAmount,
                deviceId = fpLock.lockedPumpState.deviceId
            )
        )
        val txnItems = listOf(fuelTxnItem)
        // Save sale transaction directly
        val saleTxn = SaleTransaction(
            txnId = generateTransactionId(
                storeCode = _storeConfig.value?.storeCode ?: "",
                posNumber = _storeConfig.value?.posNumber ?: "1"
            ),
            txnStartTime = Date(),
            cashierId = cashierId(),
            txnStatus = TransactionStatus.FuelAuthorized,
            statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.FuelAuthorized),
            txnItems = txnItems,
            fuelMeta = FuelMeta(
                authorizedPumps = listOf(PumpMeta(fpLock.lockedPumpState.deviceId))
            ),
            txnEndTime = null
        )
        _uiState.update { it.closeFPDialog() }
        val saved = transactionRepository.saveTransaction(saleTxn)
        if (saved) {
            authorizePrepayPump(saleTxn, TxnPaymentType.Cash)
        }
    }

    fun onUnlockPump(state: FuelPumpState) = viewModelScope.launch {
        when (state.fpState?.fpState?.deviceState) {
            DeviceState.FDC_LOCKED -> freeFuelPump(state)
            DeviceState.FDC_AUTHORISED -> terminateFuelPump(state)
            else -> {}
        }
        _uiState.update { it.closeFPDialog() }
    }

    fun onPresetVolumeEnter(
        volume: Float,
        state: FuelPumpState
    ) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch

        selectedFuelProduct?.let { fuelProduct ->
            val price = fuelProduct.fuelPrice.find { it.modeNo == FUEL_CASH_MODE }?.price
            val amount = price?.multiply(volume.toBigDecimal())
            val requestId = generatePumpRequestId(state.deviceId)
            launch {
                reserveFP.reserveFPResponse.collectLatest { response ->

                    if (response.overallResult == OverallResult.SUCCESS) {
                        if (response.isForCurrentDevice(
                                fdcConfig.workstationID,
                                requestId
                            )
                        ) {
                            val fpLock = FPLockState(
                                lockedAmount = (amount ?: 0).toDouble().to2Decimal(),
                                lockedVolume = volume.toDouble(),
                                lockedPrice = price?.toDouble(),
                                lockedPumpState = state,
                                transactionItemId = UUID.randomUUID().toString()
                            )
                            _fdcState.update { it.addFpLock(fpLock) }
                            _uiState.update { it.closeFPDialog() }
                            dashboardStateProvider.addTxnItem(
                                TransactionItemWithFuel(
                                    transactionItemId = fpLock.transactionItemId,
                                    preFuel = PreFuel(
                                        pumpNo = fpLock.lockedPumpState.pumpNo,
                                        amount = fpLock.lockedAmount,
                                        volume = fpLock.lockedVolume,
                                        unitPrice = fpLock.lockedPrice,
                                        deviceId = fpLock.lockedPumpState.deviceId,
                                        productNo = fuelProduct.productNo
                                    )
                                )
                            )
                        }
                    } else {
                        _toastMessage.emit(ToastMessage("${response.overallResult.value()} : Failed to reserve fuel"))
                    }
                }
            }

            launch {
                reserveFP(
                    ReserveFuelPointRequestType().apply {
                        applicationSender = fdcConfig.applicationSender
                        workstationID = fdcConfig.workstationID
                        requestID = requestId
                        poSdata = POSdataBaseType().apply {
                            posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                            deviceClass = DeviceClassType().apply {
                                type = Type.FP
                                deviceID = state.deviceId
                            }
                        }
                    }
                )
            }
        }
    }

    fun clearFPFreeState() = viewModelScope.launch {
        _freeFPState.emit(null)
    }

    fun onCartItemDelete(item: TransactionItemWithFuel) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch

        val pumpRequestId = generatePumpRequestId(item.preFuel.pumpNo)
        launch {
            freeFP.freeFPResponse.collectLatest { response ->

                if (response.overallResult == OverallResult.SUCCESS) {
                    if (response.isForCurrentDevice(
                            fdcConfig.workstationID,
                            pumpRequestId
                        )
                    ) {
                        _freeFPState.emit(item)
                        _fdcState.update { it.deletedFpLock(item.preFuel.deviceId) }
                    }
                } else {
                    _toastMessage.emit(ToastMessage("${response.overallResult.value()} : Failed to free fuel pump"))
                }


            }
        }

        launch {
            freeFP(
                FreeFuelPointRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = pumpRequestId
                    poSdata = POSdataBaseType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            type = Type.FP
                            deviceID = item.preFuel.deviceId
                        }
                    }
                }
            )
        }
    }

    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
        _fdcConfig.emit(
            FDCConfig(workstationID = storeConfig.posId, applicationSender = storeConfig.posId)
        )
    }

    fun updateStoreLevelConfig(storeLevelConfig: StoreUsers) = viewModelScope.launch {
        _storeLevelConfig.emit(storeLevelConfig)
    }

    fun onViewTransactionsClick(selectedPump: FuelPumpState) = viewModelScope.launch {
        _uiState.update {
            it.openFPTransactionDialog(selectedPump).copy(
                transactions = transactionRepository.getFuelTransactions(
                    selectedPump.deviceId,
                )
            )
        }
    }

    fun areAllPumpsReady() = _fdcState.value.areAllPumpsReady()

    fun updateCurrentTransaction(
        currentTransaction: Transaction?
    ) = viewModelScope.launch {
        currentTransaction?.txnId?.let { txnId ->
            _fdcState.update { it.copy(linkedTxnId = txnId) }
        }
    }

    fun freeFuelPumps(items: List<TransactionItemWithFuel>) = viewModelScope.launch {
        items.forEach { item ->
            val fuelPump = _fdcState.value.pumps().find { it.deviceId == item.preFuel.deviceId }
            fuelPump?.let { freeFuelPump(it) }
        }
    }

    private fun terminateFuelPump(fuelPump: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch
        val requestId = generatePumpRequestId(fuelPump.pumpNo)
        launch {
            terminateFP
                .terminateFPResponse
                .collectLatest { response ->
                    if (response.isForCurrentDevice(
                            workstationId = fdcConfig.workstationID,
                            requestID = requestId
                        )
                        && response.overallResult == OverallResult.SUCCESS
                    ) {
                        launch {
                            freeFuelPump(fuelPump)
                        }
                    }
                }
        }

        launch {
            terminateFP.invoke(
                TerminateFuelPointRequestType().apply {
                    applicationSender = fdcConfig.applicationSender
                    workstationID = fdcConfig.workstationID
                    requestID = requestId
                    poSdata = POSdataBaseType().apply {
                        posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                        deviceClass = DeviceClassType().apply {
                            type = Type.FP
                            deviceID = fuelPump.deviceId
                        }
                    }
                }
            )
        }
    }

    private fun freeFuelPump(fuelPump: FuelPumpState) = viewModelScope.launch {
        val fdcConfig = _fdcConfig.value ?: return@launch
        val pumpRequestId = generatePumpRequestId(fuelPump.pumpNo)
        freeFP(
            FreeFuelPointRequestType().apply {
                applicationSender = fdcConfig.applicationSender
                workstationID = fdcConfig.workstationID
                requestID = pumpRequestId
                poSdata = POSdataBaseType().apply {
                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                    deviceClass = DeviceClassType().apply {
                        type = Type.FP
                        deviceID = fuelPump.deviceId
                    }
                }
            }
        )
    }
}