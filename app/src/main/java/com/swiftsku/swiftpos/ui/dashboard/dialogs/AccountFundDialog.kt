package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.ui.theme.Blue


enum class FundOption {
    CASH, CARD, CHECK, ADJUSTMENT
}

@Composable
fun AccountFundDialog(
    onOptionSelect: (fundOptions: FundOption) -> Unit,
    onCancel: () -> Unit
) {
    Dialog(
        onDismissRequest = { onCancel() }, properties = DialogProperties(
            dismissOnBackPress = true, dismissOnClickOutside = true
        )
    ) {
        Surface {
            Row(Modifier.padding(16.dp), horizontalArrangement = Arrangement.SpaceBetween) {
                Box(
                    Modifier
                        .width(100.dp)
                        .height(60.dp)
                        .background(Blue, RoundedCornerShape(8.dp))
                        .clickable { onOptionSelect(FundOption.CASH) }) {
                    Text(
                        modifier = Modifier.align(Alignment.Center),
                        text = "Cash",
                        color = Color.White
                    )
                }
                Spacer(Modifier.width(20.dp))
                Box(
                    Modifier
                        .width(100.dp)
                        .height(60.dp)
                        .background(Blue, RoundedCornerShape(8.dp))
                        .clickable { onOptionSelect(FundOption.CARD) }) {
                    Text(
                        modifier = Modifier.align(Alignment.Center),
                        text = "Card",
                        color = Color.White
                    )
                }
                Spacer(Modifier.width(20.dp))
                Box(
                    Modifier
                        .width(100.dp)
                        .height(60.dp)
                        .background(Blue, RoundedCornerShape(8.dp))
                        .clickable { onOptionSelect(FundOption.CHECK) }) {
                    Text(
                        modifier = Modifier.align(Alignment.Center),
                        text = "Check",
                        color = Color.White
                    )
                }
                Spacer(Modifier.width(20.dp))
                Box(
                    Modifier
                        .width(100.dp)
                        .height(60.dp)
                        .background(Blue, RoundedCornerShape(8.dp))
                        .clickable { onOptionSelect(FundOption.ADJUSTMENT) }) {
                    Text(
                        modifier = Modifier.align(Alignment.Center),
                        text = "Adjustment",
                        color = Color.White
                    )
                }
            }
        }
    }
}