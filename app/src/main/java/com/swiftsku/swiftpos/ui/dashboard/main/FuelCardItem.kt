package com.swiftsku.swiftpos.ui.dashboard.main

import android.media.SoundPool
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.withFrameNanos
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import com.fdc.core.types.DeviceState
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.PumpMeta
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.fuelAmount
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.icon
import com.swiftsku.swiftpos.ui.dashboard.main.state.isFueling
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.LitePurple
import com.swiftsku.swiftpos.ui.theme.Red
import java.util.Locale

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FuelCardItem(
    modifier: Modifier = Modifier,
    backgroundColor: Color = LitePurple,
    pumpState: FuelPumpState,
    onClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
    fdcVM: FDCViewModel
) {
    val fdcUiState by fdcVM.uiState.collectAsState()
    val unpaidFuelSaleTxn = fdcUiState.unpaidFuelTxns.find {
        it.fuelMeta?.authorizedPumps?.contains(PumpMeta(pumpState.deviceId)) == true
    }
    val unpaidAmt = unpaidFuelSaleTxn?.fuelAmount

    val context = LocalContext.current
    var soundPool: SoundPool? by remember { mutableStateOf(null) }
    var soundId: Int? by remember { mutableStateOf(null) }
    var isSoundPlayed by remember { mutableStateOf(false) }
    var currentState by remember { mutableStateOf<DeviceState?>(null) }
    var isFromReadyToCalling by remember { mutableStateOf(false) }
    var blinking by remember { mutableStateOf(true) }
    val alpha = remember { Animatable(1f) }
    LaunchedEffect(Unit) {
        soundPool = SoundPool.Builder()
            .setMaxStreams(1)
            .build()
        soundId = soundPool?.load(context, R.raw.ding, 1)
    }

    DisposableEffect(Unit) {
        onDispose {
            soundPool?.release()
            soundPool = null
        }
    }
    val deviceState = pumpState.fpState?.fpState?.deviceState
    LaunchedEffect(deviceState) {
        /*
            If pump state is changing from FDC_READY to FDC_CALLING, it's a postpay request,
            so we can ring the bell and also "car next to pump(FuelRequested)" icon can be used.
            Else if the new state isn't FDC_CALLING, state has changed to something else, so we can
            reset these 2 states, isFromReadyToCalling & isSoundPlayed.
            Even if we receive the same FDC_CALLING state, both of these states will not change and
            we will continue to show correct icon and won't ring the bell again.
         */
        if (currentState == DeviceState.FDC_READY &&
            (deviceState == DeviceState.FDC_CALLING || deviceState == DeviceState.FDC_REQUESTED)
        ) {
            isFromReadyToCalling = true
            // Play the sound
            if (soundId != null && !isSoundPlayed) {
                soundPool?.play(soundId!!, 1f, 1f, 1, 0, 1f)
                isSoundPlayed = true
                blinking = true
            }
        } else if (deviceState != DeviceState.FDC_CALLING && deviceState != DeviceState.FDC_REQUESTED) {
            isSoundPlayed = false
            isFromReadyToCalling = false
        }
        currentState = deviceState
    }

    LaunchedEffect(blinking) {
        if (blinking) {
            val duration = 500L
            val startTime = withFrameNanos { it }
            while (blinking && (withFrameNanos { it } - startTime) < 5_000_000_000L) {
                alpha.animateTo(
                    targetValue = 0.3f,
                    animationSpec = tween(durationMillis = duration.toInt(), easing = LinearEasing)
                )
                alpha.animateTo(
                    targetValue = 1f,
                    animationSpec = tween(durationMillis = duration.toInt(), easing = LinearEasing)
                )
            }
            blinking = false
        }
        // Ensure alpha returns to normal after blinking stops
        // Linter is wrong here
        if (!blinking && alpha.value != 1f) {
            alpha.snapTo(1f)
        }
    }

    val isWatching = fdcVM.isWatchingPump(pumpState.deviceId)
    val shouldWatch = pumpState.isFueling
    if (isWatching && !shouldWatch) {
        fdcVM.stopWatchingPump(pumpState.deviceId)
    }

    val svgFile = when {
        isFromReadyToCalling -> "file:///android_asset/svg/FuelRequested.svg"
        isWatching && shouldWatch -> "file:///android_asset/svg/FuelWatching.svg"
        else -> "file:///android_asset/svg/${pumpState.icon}.svg"
    }
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data(svgFile)
            .decoderFactory(SvgDecoder.Factory())
            .build()
    )
    val currentAmount =
        pumpState.currentFuelStatus?.currentAmount?.toDouble()?.toDollars().orEmpty()
    val currentVolume =
        "${String.format(Locale.US, "%.2f", pumpState.currentFuelStatus?.currentVolume)} gal"

    Card(
        backgroundColor = backgroundColor,
        modifier = modifier
            .padding(4.dp)
            .fillMaxWidth()
            .height(60.dp)
            .let {
                if (onLongClick != null) it.combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                ) else it.clickable(onClick = {
                    blinking = false
                    onClick()
                })
            }
            .testTag("${pumpState.pumpNo}"),
        elevation = 8.dp,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(RoundedCornerShape(bottomStart = 16.dp))
                    .background(Color.White.copy(alpha = .12f))
                    .align(Alignment.TopEnd),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "${pumpState.pumpNo}",
                    style = MaterialTheme.typography.subtitle1,
                    color = Color.White,
                )
            }
            unpaidAmt?.let {
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(bottomEnd = 16.dp))
                        .background(Color.White.copy(alpha = .8f))
                        .align(Alignment.TopStart),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(start = 6.dp, top = 4.dp, end = 6.dp, bottom = 4.dp),
                        text = unpaidAmt.toDollars(),
                        style = MaterialTheme.typography.subtitle1,
                        color = Red,
                    )
                }
            }
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .alpha(alpha.value)
            )
            if (isWatching && shouldWatch) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.align(Alignment.BottomCenter)
                ) {
                    Text(
                        text = currentAmount,
                        style = MaterialTheme.typography.caption,
                        color = Color.White
                    )
                    Text(
                        text = currentVolume,
                        style = MaterialTheme.typography.caption,
                        color = Color.White
                    )
                }
            }
        }
    }
}