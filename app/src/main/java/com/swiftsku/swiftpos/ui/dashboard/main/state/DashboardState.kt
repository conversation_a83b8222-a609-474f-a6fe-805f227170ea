package com.swiftsku.swiftpos.ui.dashboard.main.state

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.EODReport
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.data.model.LinkedPluData
import com.swiftsku.swiftpos.data.model.LoyaltyState
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.Promotion
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.type.NumPadResult
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentResponseWS
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.RefundState

data class DashboardState(
    val customerCount: Int = 0,
    val totalSales: Float = 0f,
    val priceBookList: List<PluItem> = emptyList(),
    val departmentList: List<Department> = emptyList(),
    val transactionSummary: TransactionSummary = TransactionSummary(),
    val promotionList: List<Promotion> = listOf(),
    val cartItems: List<TransactionItem> = listOf(),
    val loading: String? = null,
    val customerMinimumAge: Int = 0,
    val showAgeVerificationDialog: Boolean = false,
    val searchQuery: String = "",
    val numPadResult: NumPadResult? = null,
    val requestToCloseCashDrawer: Boolean = false,
    val gUpcItem: GUPCItem? = null,
    val showEBTDialog: Boolean = false,
    val enteredEbtAmount: Float? = null,
    val isPriceCheck: Boolean = false,
    val priceCheckItem: PluItem? = null,
    val showSupportDialog: Boolean = false,
    val taxList: List<Tax> = emptyList(),
    val showCouponDialog: Boolean = false,
    val showCheckDialog: Boolean = false,
    val coupons: List<Coupon> = emptyList(),
    val appliedFees: List<AppliedFee> = emptyList(),
    val payout: PayoutState = PayoutState(),
    val errorDialogMessage: String? = null,
    val paymentState: PaymentState = PaymentState(),
    val showReportDialog: Boolean = false,
    val selectedEODReportDate: String? = null,
    val eodReport: EODReport? = null,
    val barcodePriceBookList: List<PluItem> = emptyList(),
    val loyaltyState: LoyaltyState = LoyaltyState(),
    val showUserPhoneNumberInput: Boolean = false,
    val showInfo: Boolean = false,
    val showVoidConfirmation: Boolean = false,
    val showCardPaymentDialog: Boolean = false,
    val showEbtPaymentDialog: Boolean = false,
    val showCreditPaymentDialog: Boolean = false,
    val showPbSearchDialog: Boolean = false,
    val showLotteryDetails: Boolean = false,
    val showIpInput: Boolean = false,
    val showRefundDialog: Boolean = false,
    val showBatchCloseConfirmation: Boolean = false,
    val showSaveDialog: Boolean = false,
    val showVendorDialog: Boolean = false,
    val showPendingFuelCardTxns: Boolean = false,
    val showCreditAccounts: Boolean = false,
    val showFundThruCash: Boolean = false,
    val showFundThruCard: Boolean = false,
    val showFundThruCheck: Boolean = false,
    val showFundThruAdjustment: Boolean = false,
    val showAccountSelection: Boolean = false,
    val linkedPluData: List<LinkedPluData> = emptyList(),
    val linkedPluItems: List<PluItem> = emptyList(),
    val refundState: RefundState? = null,

    val enteredCardAmount: Float? = null,
    val fuelPriceDiff: Double = 0.0,
    val updatedFuelItems: List<TransactionItem>? = null,

    val cashPaymentState: CashPaymentState = CashPaymentState(),
    val cardPaymentState: CardPaymentState = CardPaymentState(),
    val ebtPaymentState: EBTPaymentState = EBTPaymentState(),
    val checkPaymentState: CheckPaymentState = CheckPaymentState(),
    val creditPaymentState: CreditPaymentState = CreditPaymentState(),

    val paymentResponseWS: PaymentResponseWS? = null,
    val transactionId: String? = null,

    val selectedMenuKeyId: String? = null,
    val selectedEbtType: EbtType? = null,
    val selectedDeptForDetail: Department? = null,
    val selectedCreditAccount: CreditAccount? = null,

    val ebtBalancesCents: MutableMap<EbtType, Float?> = mutableMapOf(),
    val availableOffers: List<String> = emptyList(),
)

data class PaymentState(
    val message: ToastMessage? = null,
    val attempts: Int = 0,
    val showRetry: Boolean = false,
    val transactionId: String? = null,
    val showPaymentDialog: Boolean = false,
    val paymentEpoch: Int = -1,
    val retrying: Boolean = false,
    val retryCount: Int = 0,
)

data class TransactionSummary(
    val transactionTotalNetAmount: Float = 0f,
    val transactionTotalGrossAmount: Float = 0f,
    val transactionTotalTaxNetAmount: Float = 0f,
    val transactionTotalGrandAmount: Float = 0f,
    val cashAmountCollected: Float = 0f,
    val cardAmountCollected: Float = 0f,
    val ebtAmountCollected: Float = 0f,
    val checkAmountCollected: Float = 0f,
    val creditAmountCollected: Float = 0f,
    val promotionApplied: Float = 0f,
    val ebtAmount: Float = 0f,
    val ebtPromotion: Float = 0f,
    val ebtLoyalty: Float = 0f,
    val ebtTax: Float = 0f,
    val couponAmount: Float = 0f,
    val lotteryAmount: Float = 0f,
    val allEBT: Boolean = false,
    val calculatedFees: List<AppliedFee> = emptyList()
)

data class GUPCItem(
    val pluItem: PluItem,
    val showReport: Boolean = true,
)

fun TransactionSummary.grandTotal(): Float {
    var total = transactionTotalGrandAmount
    if (couponAmount >= total) {
        total = 0f
    } else {
        total -= couponAmount
    }
    total -= lotteryAmount
    return total
}

fun TransactionSummary.pendingAmount(): Float {
    val pending = grandTotal() - totalAmountCollected()
    return pending.to2Decimal()
}

fun TransactionSummary.change(): Float {
    var change = totalAmountCollected() - grandTotal()

    /* Note: This is no longer needed because while calculating the taxes, if the item is EBT,
     * taxation map is emptied. (Check UpdatePriceBookTxnItemsUseCase, val discardTaxation = it.isEBT() && transactionSummary.ebtAmountCollected > 0)
     * In case partial EBT amount is paid, we calculate tax on the remaining amount.
     * So taxation map is update by EbtCalculationsUseCase.
     */
//    if (ebtAmountCollected > 0) {
//        change += ebtTax
//
//        if (allEBT) {
//            change = 0f
//        }
//    }

    if (change < 0) {
        change = 0f
    }


    return change.to2Decimal()
}

fun TransactionSummary.totalAmountCollected(): Float =
    (ebtAmountCollected + cashAmountCollected + cardAmountCollected + checkAmountCollected + creditAmountCollected).to2Decimal()

fun TransactionSummary.totalEBT(): Float = (ebtAmount - ebtPromotion - ebtLoyalty).to2Decimal()
fun TransactionSummary.remainingAfterEBT(): Float {

    val remaining = grandTotal() - totalEBT() - ebtTax

    if (grandTotal() <= 0) {
        return 0f
    }
    return remaining.to2Decimal()
}


fun TransactionSummary.disableCard() = grandTotal() <= 0 || cardAmountCollected > 0f

fun TransactionSummary.disableEBT() = grandTotal() <= 0 || ebtAmountCollected > 0

val DashboardState.hasPostFuelInCart: Boolean
    get() = cartItems.any { it is TransactionItemWithFuel && it.postFuel != null }

val DashboardState.allowAddingItemsToCart: Boolean
    get() = if (hasPostFuelInCart) {
        true
    } else transactionSummary.totalAmountCollected() == 0f
