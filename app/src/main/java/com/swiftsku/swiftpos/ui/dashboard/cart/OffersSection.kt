package com.swiftsku.swiftpos.ui.dashboard.cart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowCircleLeft
import androidx.compose.material.icons.filled.ArrowCircleRight
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.MixMatchEntry
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Grey_D6
import com.swiftsku.swiftpos.ui.theme.Teal


@Composable
fun OffersSection(
    availableOffers: List<String>
) {
    var expanded by remember { mutableStateOf(true) }
    val buttonText = if (expanded) "Hide" else "Offers"
    val arrowIcon = if (expanded) Icons.Default.KeyboardArrowDown else Icons.Default.KeyboardArrowUp

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Button(
            onClick = { expanded = !expanded },
            modifier = Modifier.height(36.dp),
            colors = ButtonDefaults.buttonColors(backgroundColor = Blue),
            shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
        ) {
            Text(text = buttonText, color = Color.White)
            Spacer(modifier = Modifier.width(4.dp))
            Icon(imageVector = arrowIcon, contentDescription = null, tint = Color.White)
        }
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            if (expanded) {
                CarouselSection(availableOffers)
            }
        }
    }
}

@Composable
fun CarouselSection(
    availableOffers: List<String>
) {
    var currentIndex by remember(availableOffers) { mutableIntStateOf(0) }

    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            IconButton(
                onClick = { if (currentIndex > 0) currentIndex-- },
                enabled = (currentIndex > 0)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowCircleLeft,
                    contentDescription = "Previous",
                    modifier = Modifier.size(36.dp),
                    tint = if (currentIndex > 0) Blue else Grey_D6
                )
            }
            Box(
                modifier = Modifier
                    .height(42.dp)
                    .weight(1f)
                    .background(Teal, shape = RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = availableOffers[currentIndex],
                    style = MaterialTheme.typography.body1,
                    color = Color.White,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp, end = 8.dp),
                    textAlign = TextAlign.Start,
                    fontWeight = FontWeight.Medium
                )
            }
            IconButton(
                onClick = { if (currentIndex < availableOffers.size - 1) currentIndex++ },
                enabled = (currentIndex < availableOffers.size - 1)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowCircleRight,
                    contentDescription = "Next",
                    modifier = Modifier.size(36.dp),
                    tint = if (currentIndex < availableOffers.size - 1) Blue else Grey_D6
                )
            }
        }
        Spacer(modifier = Modifier.height(2.dp))
    }
}

fun getPromoText(description: String, mixMatchEntry: MixMatchEntry): String? {
    val mixMatchUnits = mixMatchEntry.mixMatchUnits.value.toInt()
    val discountAmt = mixMatchEntry.mixMatchDiscountAmount?.value
    val discountPerc = mixMatchEntry.mixMatchDiscountPercent?.value
    val mixMatchPrice = mixMatchEntry.mixMatchPrice?.value
    return when {
        mixMatchUnits > 0 && discountAmt != null -> "Buy $mixMatchUnits ${description.take(33)}, Get ${discountAmt.toDollars()} off"
        mixMatchUnits > 0 && discountPerc != null -> "Buy $mixMatchUnits $description, Get $discountPerc% off"
        mixMatchUnits > 0 && mixMatchPrice != null -> "Buy $mixMatchUnits $description for $$mixMatchPrice"
        else -> null
    }
}