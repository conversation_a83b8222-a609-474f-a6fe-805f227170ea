package com.swiftsku.swiftpos.ui.dashboard.txnhistory

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TextButton
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavController
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.totalAmount
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.extension.toDateTime
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.extension.toTime
import com.swiftsku.swiftpos.ui.components.EndlessLazyColumn
import com.swiftsku.swiftpos.ui.components.TimePickerDialog
import com.swiftsku.swiftpos.ui.navigation.Routes
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset


@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class)
@Composable
fun TxnHistory(
    navController: NavController,
    transactionHistoryVM: TxnHistoryViewModel
) {
    val coroutineScope = rememberCoroutineScope()
    val state by transactionHistoryVM.newUiState.collectAsState()

    LaunchedEffect(Unit) {
        transactionHistoryVM.clearFilter()
    }

    val startDatePickerState = rememberDatePickerState(
        yearRange = IntRange(2024, LocalDate.now().year + 1)
    )
    val endDatePickerState = rememberDatePickerState(
        yearRange = IntRange(2024, LocalDate.now().year + 1)
    )
    val startTimePickerState: TimePickerState = rememberTimePickerState(
        is24Hour = true,
        initialHour = state.selectedStartTime.hour,
        initialMinute = state.selectedStartTime.minute
    )
    val endTimePickerState: TimePickerState = rememberTimePickerState(
        is24Hour = true,
        initialHour = state.selectedEndTime.hour,
        initialMinute = state.selectedEndTime.minute
    )

    Surface(
        modifier = Modifier
            .padding(top = 100.dp, bottom = 100.dp)
            .background(Color.White)
            .width(750.dp)
            .semantics { testTagsAsResourceId = true }
    ) {
        Column {
            TopAppBar(
                elevation = 4.dp,
                title = { Text("Transaction History") },
                actions = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Filled.Clear, null)
                    }
                }
            )

            Column(
                modifier = Modifier.weight(1f),
            ) {
                TxnFilter(
                    query = state.query,
                    onQueryChange = transactionHistoryVM::onSearchQueryChange,
                    onDebounceQueryChange = transactionHistoryVM::onDebounceSearch,
                    txnFilterState = state,
                    onTimeClick = transactionHistoryVM::showTimePicker,
                    onClearFilter = transactionHistoryVM::clearFilter,
                    onDateClick = transactionHistoryVM::showDatePicker
                )
                TxnHistoryHeader()

                Box(modifier = Modifier.fillMaxSize()) {
                    if (state.isLoading) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    } else if (state.transactions.isEmpty()) {
                        Text(
                            "No transactions found",
                            modifier = Modifier.align(Alignment.Center),
                            textAlign = TextAlign.Center
                        )
                    } else {
                        EndlessLazyColumn(
                            modifier = Modifier
                                .fillMaxSize()
                                .testTag("txn_history"),
                            items = state.transactions,
                            itemKey = { item -> item.txnId },
                            itemContent = { transaction: Transaction ->
                                val formattedAmount = transaction.totalAmount().toDollars()
                                TxnHistoryItem(
                                    transactionId = transaction.txnId,
                                    time = getTimeToShow(transaction, state),
                                    itemsCount = "${transaction.txnItems.filter { it.status == TransactionItemStatus.Normal }.size}",
                                    totalAmount = formattedAmount,
                                    onItemClick = {
                                        navController.navigate(Routes.TxnHistoryDetail.route + "/${transaction.txnId}")
                                    },
                                    type = transaction.txnType
                                )
                            },
                            loadMore = {
                                transactionHistoryVM.loadNextPage()
                            },
                            isPaginating = state.isPaginating,
                            endReached = state.endReached
                        )
                    }
                }

            }
        }
    }

    val showDatePicker = state.showDatePicker
    if (showDatePicker != null) {
        val pickerState = when (showDatePicker) {
            DatePickerType.Start -> startDatePickerState
            DatePickerType.End -> endDatePickerState
        }
        DatePickerDialog(
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false
            ),
            onDismissRequest = transactionHistoryVM::hideDatePicker,
            confirmButton = {
                TextButton(onClick = {
                    pickerState.selectedDateMillis?.let {
                        transactionHistoryVM.hideDatePicker()
                        val date = Instant
                            .ofEpochMilli(it)
                            .atOffset(ZoneOffset.UTC)
                            .toLocalDate()
                        when (showDatePicker) {
                            DatePickerType.Start -> transactionHistoryVM.onStartDateAvailable(date)
                            DatePickerType.End -> transactionHistoryVM.onEndDateAvailable(date)
                        }
                    }
                }) {
                    Text(text = "Confirm")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = transactionHistoryVM::hideDatePicker
                ) {
                    androidx.compose.material3.Text(text = "Cancel")
                }
            }

        ) {
            DatePicker(state = pickerState)
        }
    }

    val showTimePicker = state.showTimePicker
    if (showTimePicker != null) {
        val pickerState = when (showTimePicker) {
            TimePickerType.Start -> startTimePickerState
            TimePickerType.End -> endTimePickerState
        }
        TimePickerDialog(
            onCancel = transactionHistoryVM::hideTimePicker,
            onConfirm = {
                val localTime = LocalTime.of(pickerState.hour, pickerState.minute)
                transactionHistoryVM.hideTimePicker()

                when (showTimePicker) {
                    TimePickerType.Start -> transactionHistoryVM.onStartTimeAvailable(localTime)
                    TimePickerType.End -> transactionHistoryVM.onEndTimeAvailable(localTime)
                }

                coroutineScope.launch {
                    pickerState.settle()
                }
            }
        ) {
            TimePicker(state = pickerState)
        }
    }
}

fun getTimeToShow(transaction: Transaction, state: TransactionHistoryUiState): String {
    val timestamp = transaction.txnEndTime ?: transaction.txnStartTime
    return if (state.selectedStartDate == state.selectedEndDate) {
        timestamp.toTime()
    } else {
        timestamp.toDateTime()
    }
}