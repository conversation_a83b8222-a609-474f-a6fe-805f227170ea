package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import java.util.Date

data class TxnHistoryState(
    val transaction: Transaction? = null,
    val refunding: Boolean = false,
    val refundState: RefundState? = null,
    val showConfirm: Boolean = false,
)

fun TxnHistoryState.cardPayment(): CardPayment? {
    if (transaction is SaleTransaction) {
        return transaction.txnPayment[TxnPaymentType.Card] as? CardPayment
    }
    return null
}

fun TxnHistoryState.cashPayment(): CashPayment? {
    if (transaction is SaleTransaction) {
        return transaction.txnPayment[TxnPaymentType.Cash] as? CashPayment
    }
    return null
}

fun TxnHistoryState.ebtPayment(): EBTPayment? {
    if (transaction is SaleTransaction) {
        return transaction.txnPayment[TxnPaymentType.EBT] as? EBTPayment
    }
    return null
}

fun TxnHistoryState.checkPayment(): ChequePayment? {
    if (transaction is SaleTransaction) {
        return transaction.txnPayment[TxnPaymentType.Cheque] as? ChequePayment
    }
    return null
}

data class RefundState(val startTime: Date, val refundAmount: Float = 0f)

