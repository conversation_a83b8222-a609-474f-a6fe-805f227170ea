package com.swiftsku.swiftpos.ui.dashboard.main

import android.icu.text.SimpleDateFormat
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.components.MenuIconButton
import com.swiftsku.swiftpos.ui.components.MiniTile
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.theme.DisabledBackground
import com.swiftsku.swiftpos.ui.theme.blackAndWhite
import com.swiftsku.swiftpos.ui.theme.clickableTextColor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.Locale

@Composable
fun ColumnScope.MainFooterNew(
    modifier: Modifier = Modifier,
    onPriceCheckClick: (Int) -> Unit,
    onRecallClick: (Int) -> Unit,
    onTxnHistoryClick: (Int) -> Unit,
    onReportClick: (Int) -> Unit,
    onInfoClick: () -> Unit,
    onMenuClick: () -> Unit,
    totalSale: Float,
    customerCount: Int,
    dashboardVM: DashboardViewModel
) {
    val settings by dashboardVM.userSettings.collectAsState()

    Card(shape = RoundedCornerShape(0)) {
        Row(
            modifier = modifier
                .weight(1f, true)
                .padding(start = 20.dp)
                .height(64.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ClickableText(
                    modifier = Modifier.testTag("footer_price_check"),
                    text = AnnotatedString("Price Check"),
                    onClick = onPriceCheckClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_recall"),
                    text = AnnotatedString("Recall"), onClick = onRecallClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_txn_history"),
                    text = AnnotatedString("Txn History"), onClick = onTxnHistoryClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_reports"),
                    text = AnnotatedString("Reports"), onClick = onReportClick,
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
                ClickableText(
                    modifier = Modifier.testTag("footer_info"),
                    text = AnnotatedString("Info"), onClick = { onInfoClick() },
                    style = MaterialTheme.typography.body1.copy(color = MaterialTheme.colors.clickableTextColor)
                )
            }
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End
            ) {
                if (settings?.displayTotals.isTrue()) {
                    val formattedTotalSaleAmount = totalSale.toDollars()
                    MiniTile(
                        "Total Sales",
                        formattedTotalSaleAmount,
                        Modifier.padding(start = 8.dp, end = 8.dp)
                    )
                    MiniTile(
                        "Customer Count",
                        "$customerCount",
                        Modifier.padding(start = 8.dp, end = 8.dp)
                    )
                }
                DateComponentNew()
                MenuIconButton(onClick = onMenuClick)
            }
        }

    }
}

@Composable
fun DateComponentNew() {
    var date by remember { mutableStateOf(Pair("", "")) }
    LaunchedEffect(Unit) {
        while (true) {
            var formattedDate: String
            var formattedTime: String

            withContext(Dispatchers.IO) {
                val currentDate = Date()
                val dateFormat = SimpleDateFormat("EEE, MMM d", Locale.US)
                val timeFormat = SimpleDateFormat("h:mm a", Locale.US)

                formattedDate = dateFormat.format(currentDate)
                formattedTime = timeFormat.format(currentDate)
            }

            date = Pair(formattedDate, formattedTime)
            delay(1000)
        }
    }
    Box(modifier = Modifier
        .padding(start = 8.dp, end = 16.dp, top = 8.dp, bottom = 8.dp)
        .background(DisabledBackground)) {
        Column(
            modifier = Modifier.padding(4.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = date.first,
                style = MaterialTheme.typography.body1,
                color = MaterialTheme.colors.blackAndWhite
            )
            Text(
                text = date.second,
                style = MaterialTheme.typography.subtitle1,
                color = MaterialTheme.colors.blackAndWhite
            )
        }
    }
}