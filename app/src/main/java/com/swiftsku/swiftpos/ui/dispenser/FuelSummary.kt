package com.swiftsku.swiftpos.ui.dispenser

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.swiftsku.swiftpos.data.model.FuelSaleTrxState
import com.swiftsku.swiftpos.ui.theme.GrayBackground

@Composable
fun FuelSummary(state: FuelSaleTrxState) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 16.dp)
            .border(1.dp, color = GrayBackground, shape = RoundedCornerShape(8.dp))
            .padding(16.dp)
    ) {
        Row(horizontalArrangement = Arrangement.SpaceBetween) {
            FuelSummaryItem("Prepaid Amount", "$${state.fuelSaleTrxDeviceClass.amount}")
            Spacer(modifier = Modifier.padding(8.dp))
            FuelSummaryItem("Volume", "${state.fuelSaleTrxDeviceClass.volume}")
            Spacer(modifier = Modifier.padding(8.dp))
            FuelSummaryItem("Unit Price", "$${state.fuelSaleTrxDeviceClass.unitPrice}")
        }
    }
}

@Composable
fun FuelSummaryItem(header: String, value: String) {
    Column {
        Text(text = header)
        Spacer(modifier = Modifier.padding(4.dp))
        Text(text = value)
    }
}