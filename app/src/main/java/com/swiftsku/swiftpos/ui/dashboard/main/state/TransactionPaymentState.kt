package com.swiftsku.swiftpos.ui.dashboard.main.state

sealed class TransactionPaymentState {
    abstract val completed: Boolean?
    abstract val amount: Float
    abstract val received: Boolean
}

data class CardPaymentState(
    override val completed: Boolean? = null,
    override val amount: Float = 0f,
    override val received: Boolean = false
) : TransactionPaymentState()

data class CashPaymentState(
    override val completed: Boolean? = null,
    override val amount: Float = 0f,
    override val received: Boolean = false
) : TransactionPaymentState()

data class EBTPaymentState(
    override val completed: Boolean? = null,
    override val amount: Float = 0f,
    override val received: Boolean = false
) : TransactionPaymentState()

data class CheckPaymentState(
    override val completed: Boolean? = null,
    override val amount: Float = 0f,
    override val received: Boolean = false
) : TransactionPaymentState()

fun CashPaymentState.hasDefault(): Boolean =
    this.amount == 0f && !this.received && this.completed == null

fun CardPaymentState.hasDefault(): Boolean =
    this.amount == 0f && !this.received && this.completed == null

fun EBTPaymentState.hasDefault(): Boolean =
    this.amount == 0f && !this.received && (this.completed == null || this.completed)

fun CheckPaymentState.hasDefault(): Boolean =
    this.amount == 0f && !this.received && (this.completed == null || this.completed)


fun CashPaymentState.isCompleted(): Boolean = this.completed == null || this.completed

fun CardPaymentState.isCompleted(): Boolean = this.completed == null || this.completed

fun EBTPaymentState.isCompleted(): Boolean = this.completed == null || this.completed

fun CheckPaymentState.isCompleted(): Boolean = this.completed == null || this.completed