package com.swiftsku.swiftpos.ui.presentation

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.swiftsku.swiftpos.R
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.model.unitPrice
import com.swiftsku.swiftpos.extension.toDollars

class CartListAdapter(val items: MutableList<TransactionItem>) :
    RecyclerView.Adapter<CartListAdapter.CartViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CartViewHolder {
        return CartViewHolder(
            LayoutInflater
                .from(parent.context)
                .inflate(R.layout.cart_list_item, parent, false)
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refresh(items: List<TransactionItem>) {
        this.items.clear()
        this.items.addAll(items)
        notifyDataSetChanged()
    }


    override fun getItemCount(): Int {
        return items.size
    }

    private fun getItem(position: Int): TransactionItem = items[position]


    override fun onBindViewHolder(holder: CartViewHolder, position: Int) {
        holder.setData(getItem(position))
    }

    class CartViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        fun setData(transactionItem: TransactionItem) {
            itemView.findViewById<TextView>(R.id.tvDescription).text = transactionItem.itemName()
            itemView.findViewById<TextView>(R.id.tvQty).text = "x${transactionItem.quantity}"
            itemView.findViewById<TextView>(R.id.tvAmount).text =
                transactionItem.unitPrice().toDollars()
        }
    }
}