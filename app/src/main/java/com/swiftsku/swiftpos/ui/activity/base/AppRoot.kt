package com.swiftsku.swiftpos.ui.activity.base

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalLifecycleOwner
import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.dashboard.recall.RecallViewModel
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TxnHistoryDetailViewModel
import com.swiftsku.swiftpos.ui.dashboard.txnhistory.TxnHistoryViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.login.LoginViewModel
import com.swiftsku.swiftpos.ui.navigation.SwiftPosNavGraph
import com.swiftsku.swiftpos.ui.presentation.CustomerPresentation
import com.swiftsku.swiftpos.ui.report.ReportViewModel
import com.swiftsku.swiftpos.ui.theme.SwiftPOSTheme

@Composable
fun AppRoot(
    configVM: ConfigViewModel,
    dashboardVM: DashboardViewModel,
    loginVM: LoginViewModel,
    fdcVM: FDCViewModel,
    recallVM: RecallViewModel,
    transactionHistoryVM: TxnHistoryViewModel,
    transactionHistoryDetailVM: TxnHistoryDetailViewModel,
    reportVM: ReportViewModel,
    creditsVM: CreditsViewModel,
    secondaryDisplay: CustomerPresentation?,
    fdcSocketManager: WebSocketManager
) {
    val lifecycle = LocalLifecycleOwner.current.lifecycle
    val appState by configVM.appState.collectAsState()
    val fqmState by fdcVM.fqmState.collectAsState()

    if (secondaryDisplay != null) {
        val phoneNumber by secondaryDisplay.phoneNumber.collectAsState()
        LaunchedEffect(phoneNumber) {
            phoneNumber?.let {
                dashboardVM.updateLoyaltyAccountId(it.number)
                secondaryDisplay.dismissUserInput()
            }
        }
    }

    LaunchedEffect(appState, fqmState) {
        dashboardVM.updateInfo(appState, fqmState)
    }

    LaunchedEffect(appState) {
        appState.storeConfig?.let { storeConfig ->
            secondaryDisplay?.showDisplay(storeConfig.displayConsumerScreen)
            loginVM.updateStoreConfig(storeConfig)
            dashboardVM.updateStoreConfig(storeConfig)
            fdcVM.updateStoreConfig(storeConfig)
            recallVM.updateStoreConfig(storeConfig)
            transactionHistoryDetailVM.updateStoreConfig(storeConfig)
            reportVM.updateStoreConfig(storeConfig)
            creditsVM.updateTerminalConfig(storeConfig)
        }
    }


    // start the fdc socket connection
    LaunchedEffect(appState.storeLevelConfig) {
        appState.storeLevelConfig?.let { storeLevelConfig ->
            transactionHistoryDetailVM.updateStoreLevelConfig(storeLevelConfig)
            dashboardVM.updateStoreLevelConfig(storeLevelConfig)
            creditsVM.updateStoreConfig(storeLevelConfig)
            fdcVM.updateStoreLevelConfig(storeLevelConfig)
            if (storeLevelConfig.fuelConfig?.enabled.isTrue()) {
                val url = if (storeLevelConfig.fuelConfig?.commPriority == "board") {
                    "ws://${storeLevelConfig.fuelConfig.boardIp}:36101/fqm/fdc"
                } else {
                    "ws://${storeLevelConfig.fuelConfig?.cloudPilotIp}:36101/fqm/fdc"
                }
                fdcSocketManager.registerLifeCycle(
                    lifecycle = lifecycle,
                    url = url,
                    shouldLog = storeLevelConfig.fuelConfig?.shouldLogMessages == true
                )
            }
        }
    }


    SwiftPOSTheme {
        SwiftPosNavGraph(
            secondaryDisplay = secondaryDisplay,
            appState = appState,
            dashboardVM = dashboardVM,
            loginVM = loginVM,
            fdcVM = fdcVM,
            recallVM = recallVM,
            transactionHistoryDetailVM = transactionHistoryDetailVM,
            transactionHistoryVM = transactionHistoryVM,
            reportVM = reportVM,
            creditsVM = creditsVM
        )
    }
}