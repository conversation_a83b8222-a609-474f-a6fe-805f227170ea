package com.swiftsku.swiftpos.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Menu
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp

@Composable
fun MenuIconButton(
    onClick: () -> Unit,
    icon: ImageVector = Icons.Filled.Menu
) {
    IconButton(
        onClick = onClick,
        modifier = Modifier
            .background(Color(0xFF2A85FF))
            .width(80.dp)
            .height(80.dp)
    ) {
        Icon(icon, null, modifier = Modifier.size(42.dp), tint = Color.White)
    }
}