package com.swiftsku.swiftpos.ui.presentation

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.swiftsku.swiftpos.R


class OffersAdapter(val items: MutableList<String>) :
    RecyclerView.Adapter<OffersAdapter.OfferViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OfferViewHolder {
        return OfferViewHolder(
            LayoutInflater
                .from(parent.context)
                .inflate(R.layout.offer_list_item, parent, false)
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateOffers(offers: List<String>) {
        this.items.clear()
        this.items.addAll(offers)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return items.size
    }

    private fun getItem(position: Int): String = items[position]


    override fun onBindViewHolder(holder: OfferViewHolder, position: Int) {
        holder.setData(getItem(position))
    }

    class OfferViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun setData(offer: String) {
            itemView.findViewById<TextView>(R.id.tvOfferText).text = offer
        }
    }
}