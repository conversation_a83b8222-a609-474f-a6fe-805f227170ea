package com.swiftsku.swiftpos.ui.dashboard.saletransaction

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.pax.poslinksemiintegration.constant.EbtCountType
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.RefundInfo
import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.fuelAmount
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.model.getRefundableAmounts
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.domain.transaction.PaymentRefundUseCase
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.openAsync
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import io.sentry.Sentry
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject
import com.pax.poslinkadmin.constant.TransactionType as PaxTransactionType


@HiltViewModel
class TxnHistoryDetailViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    @PrinterX private val printerXManager: ISunMiPrinter,
    private val printTransactionUseCase: PrintTransactionUseCase,
    private val userDataSource: UserDataSource,
    private val paymentUseCase: PaymentUseCase,
    private val paxPaymentService: PaxPaymentService,
    private val paymentRefundUseCase: PaymentRefundUseCase,

    ) : ViewModel() {

    private val _transaction: MutableStateFlow<Transaction?> = MutableStateFlow(null)
    private val _loading: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val _showConfirm: MutableStateFlow<Boolean> = MutableStateFlow(false)
    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    private val _refundState = MutableStateFlow<RefundState?>(null)
    val toastMessage = _toastMessage
    private var epxData: EpxData? = null
    private var epxEbtData: EpxData? = null

    val uiState: StateFlow<TxnHistoryState> =
        combine(
            _transaction,
            _loading,
            _refundState,
            _showConfirm
        ) { transaction, loading, refundState, showConfirm ->
            TxnHistoryState(
                transaction,
                refunding = loading,
                refundState = refundState,
                showConfirm = showConfirm
            )
        }.stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(),
            TxnHistoryState()
        )

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)
    private var storeLevelConfig: StoreUsers? = null


    fun updateStoreConfig(storeConfig: StoreConfig) = viewModelScope.launch {
        _storeConfig.emit(storeConfig)
    }

    fun updateStoreLevelConfig(config: StoreUsers) = viewModelScope.launch {
        storeLevelConfig = config
    }


    private suspend fun cashierId() = userDataSource.getCurrentUser()?.username ?: "cashierId"


    fun fetchTransaction(transactionId: String) = viewModelScope.launch {
        val transaction = transactionRepository.getTransaction(transactionId)
        _transaction.emit(transaction)
    }

    fun reprint() = viewModelScope.launch {
        try {
            var transaction = _transaction.value

            val storeConfig =
                _storeConfig.value

            if (transaction == null || storeConfig == null) {
                return@launch
            }

            if (transaction is RefundTransaction && transaction.refundInfo.linkedTxnId.isNotEmpty()) {
                val linkedTransaction =
                    transactionRepository.getTransaction(transaction.refundInfo.linkedTxnId)

                if (linkedTransaction is SaleTransaction) {
                    transaction = linkedTransaction
                }
            }
            val printTxnReceiptInput = printTransactionUseCase.getPrintTxnReceiptInput(
                storeConfig, transaction
            )
            printTransactionUseCase(printTxnReceiptInput)
        } catch (e: Exception) {
            EventUtils.recordException(e)
        }
    }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }

    /**
     * If there were multiple modes of payment, we first refund to digital payment sources.
     * After that we check if cash drawer has to be opened.
     */
    private fun processRefundToSource(
        transaction: SaleTransaction, storeLevelConfig: StoreUsers?
    ) = viewModelScope.launch {
        val storeConfig = _storeConfig.value ?: return@launch
        val startTime = Date()
        if (storeLevelConfig?.payfac == "epx") {
            val refundableAmounts = getRefundableAmounts(transaction)
            if (refundableAmounts.cardAmount > 0) {
                processRefundWithEpx(transaction, startTime, storeConfig.storeCode)
            } else if (refundableAmounts.ebtAmount > 0) {
                processEbtRefundWithEpx(transaction, Date(), storeConfig.storeCode)
            }
        } else {
            paymentRefundUseCase.processRefund(storeConfig, cashierId(), startTime, transaction)
                .stateIn(viewModelScope).collectLatest {
                    when (it) {
                        is Result.Success -> {
                            val refundableAmounts = getRefundableAmounts(transaction)
                            val totalRefundAmount =
                                refundableAmounts.cardAmount + refundableAmounts.ebtAmount
                            val cashPayment = transaction.txnPayment[TxnPaymentType.Cash]
                            if (cashPayment != null && cashPayment is CashPayment) {
                                _toastMessage.emit(
                                    ToastMessage(
                                        message = "${totalRefundAmount.toDollars()} refunded successfully",
                                        type = ToastMessageType.Success
                                    )
                                )
                                processCashRefund(transaction)
                            } else {
                                saveTransaction(
                                    totalRefundAmount.toFloat(),
                                    transaction,
                                    storeConfig.storeCode,
                                    startTime
                                )
                            }
                            _loading.emit(false)
                        }

                        is Result.Error -> {
                            _loading.emit(false)
                            _toastMessage.emit(
                                ToastMessage(
                                    message = it.errorMessage, type = ToastMessageType.Error
                                )
                            )
                        }

                        is Result.Loading -> {
                            _loading.emit(true)
                        }
                    }
                }
        }
    }

    private fun processRefundWithEpx(
        transaction: SaleTransaction, startTime: Date, storeId: String
    ) = viewModelScope.launch {
        val refundableAmounts = getRefundableAmounts(transaction)
        val totalRefundableAmount = refundableAmounts.cardAmount
        val refundAmount = totalRefundableAmount.convertDollarToCentPrecisely()
        paymentUseCase.processEpxCreditRequest(
            PaxPaymentInput(
                transaction.txnId,
                paxPaymentService.getCurrentBatchId(),
                refundAmount.toString(), cashierId(),
                startTime.epochInSeconds(), null,
                PaxTransactionType.RETURN
            ),
            transaction
        ).stateIn(viewModelScope).collectLatest { result ->
            when (result) {
                is Result.Loading -> _loading.emit(true)
                is Result.Error -> {
                    _loading.emit(false)
                    _toastMessage.emit(
                        ToastMessage(message = result.errorMessage, type = ToastMessageType.Error)
                    )
                }

                is Result.Success -> {
                    result.data?.let {
                        epxData = EpxData(
                            transactionType = PaxTransactionType.RETURN,
                            authorizationCode = it.hostInformation?.authorizationCode,
                            hostResponseMessage = it.hostInformation?.hostResponseMessage,
                            hostResponseNumber = it.hostInformation?.hostReferenceNumber
                        )
                        if (refundableAmounts.ebtAmount > 0) {
                            processEbtRefundWithEpx(transaction, Date(), storeId)
                            return@let
                        } else if (refundableAmounts.cashAmount > 0) {
                            _toastMessage.emit(
                                ToastMessage(
                                    message = "${(totalRefundableAmount).toDollars()} refunded successfully",
                                    type = ToastMessageType.Success
                                )
                            )
                            processCashRefund(transaction)
                        } else {
                            saveTransaction(
                                totalRefundableAmount.toFloat(), transaction, storeId, startTime,
                            )
                        }
                    }
                    _loading.emit(false)
                }
            }
        }
    }

    private fun processEbtRefundWithEpx(
        transaction: SaleTransaction, startTime: Date, storeId: String
    ) = viewModelScope.launch {
        val refundableAmounts = getRefundableAmounts(transaction)
        val totalRefundableAmount = refundableAmounts.ebtAmount
        val refundAmount = totalRefundableAmount.convertDollarToCentPrecisely()
        val ebtType = transaction.ebtInfo?.ebtType ?: EbtType.FOOD_STAMP
        val ebtCountType: EbtCountType = when (ebtType) {
            EbtType.FOOD_STAMP -> EbtCountType.FOOD_STAMP
            else -> EbtCountType.CASH_BENEFITS
        }
        paymentUseCase.processEpxEbtRequest(
            PaxPaymentInput(
                transaction.txnId,
                paxPaymentService.getCurrentBatchId(),
                refundAmount.toString(), cashierId(),
                startTime.epochInSeconds(), ebtCountType,
                PaxTransactionType.RETURN
            ),
            transaction
        ).stateIn(viewModelScope).collectLatest { result ->
            when (result) {
                is Result.Loading -> _loading.emit(true)
                is Result.Error -> {
                    _loading.emit(false)
                    _toastMessage.emit(
                        ToastMessage(message = result.errorMessage, type = ToastMessageType.Error)
                    )
                }

                is Result.Success -> {
                    result.data?.let {
                        epxEbtData = EpxData(
                            transactionType = PaxTransactionType.RETURN,
                            authorizationCode = it.hostInformation?.authorizationCode,
                            hostResponseMessage = it.hostInformation?.hostResponseMessage,
                            hostResponseNumber = it.hostInformation?.hostReferenceNumber
                        )
                        if (refundableAmounts.cashAmount > 0) {
                            _toastMessage.emit(
                                ToastMessage(
                                    message = "${(totalRefundableAmount).toDollars()} refunded successfully",
                                    type = ToastMessageType.Success
                                )
                            )
                            processCashRefund(transaction)
                        } else {
                            saveTransaction(
                                totalRefundableAmount.toFloat(), transaction, storeId, startTime
                            )
                        }
                    }
                    _loading.emit(false)
                }
            }
        }
    }

    private fun processRefundToSourceV2(transaction: SaleTransaction) = viewModelScope.launch {
        val storeConfig = _storeConfig.value ?: return@launch
        val startTime = Date()
        paymentRefundUseCase.processRefundV2(storeConfig, cashierId(), startTime, transaction)
            .stateIn(viewModelScope)
            .collectLatest {
                when (it) {
                    is Result.Success -> {
                        val refundableAmounts = getRefundableAmounts(transaction)
                        val cardRefundCents =
                            refundableAmounts.cardAmount.convertDollarToCentPrecisely()
                        val ebtRefundCents =
                            refundableAmounts.ebtAmount.convertDollarToCentPrecisely()
                        val refundingGen = cardRefundCents > 0
                        val refundingEbt = ebtRefundCents > 0
                        val genFailure = refundingGen && it.data.gen?.ok == false
                        val ebtFailure = refundingEbt && it.data.ebt?.ok == false
                        val txn = transaction.copy(refundRes = it.data)
                        _transaction.emit(txn)

                        // Case 1: Tried both refunds, both of them failed
                        if (genFailure && ebtFailure) {
                            var errorMessage = "Refund failed"
                            it.data.ebt?.msg?.let { errorMessage = it }
                            it.data.gen?.msg?.let { errorMessage = it }
                            _toastMessage.emit(
                                ToastMessage(
                                    message = errorMessage,
                                    type = ToastMessageType.Error
                                )
                            )
                        } else {
                            /*
                                Case 2: Tried both refunds, both succeeded
                                Case 3: Tried both refunds, one of them failed, one succeeded
                                Case 4: Tried single refund, it failed
                                Case 5: Tried single refund, it succeeded
                                NOTE: Handle the case where BE can send ebt as success,
                                even though only EBT refund was tried.
                             */
                            var refundedAmount = 0f
                            var anyRefundSucceed = false
                            if (refundingEbt && !ebtFailure) {
                                anyRefundSucceed = true
                                refundedAmount += ebtRefundCents
                            }
                            if (refundingGen && !genFailure) {
                                anyRefundSucceed = true
                                refundedAmount += cardRefundCents
                            }
                            val totalRefundAmount = refundedAmount / 100f
                            if (anyRefundSucceed) {
                                val cashPayment = transaction.txnPayment[TxnPaymentType.Cash]
                                if (cashPayment != null && cashPayment is CashPayment) {
                                    _toastMessage.emit(
                                        ToastMessage(
                                            message = "${totalRefundAmount.toDollars()} refunded successfully",
                                            type = ToastMessageType.Success
                                        )
                                    )
                                    processCashRefund(transaction)
                                } else {
                                    saveTransaction(
                                        totalRefundAmount, txn, storeConfig.storeCode, startTime
                                    )
                                }
                            } else {
                                var errorMessage = "Refund Failed"
                                // Check which refund failed
                                if (refundingEbt) {
                                    it.data.ebt?.msg?.let { errorMessage = it }
                                }
                                if (refundingGen) {
                                    it.data.gen?.msg?.let { errorMessage = it }
                                }
                                _toastMessage.emit(
                                    ToastMessage(
                                        message = errorMessage,
                                        type = ToastMessageType.Error
                                    )
                                )
                            }
                        }
                        _loading.emit(false)
                    }

                    is Result.Error -> {
                        _loading.emit(false)
                        _toastMessage.emit(
                            ToastMessage(
                                message = it.errorMessage,
                                type = ToastMessageType.Error
                            )
                        )
                    }

                    is Result.Loading -> {
                        _loading.emit(true)
                    }
                }
            }
    }

    private fun refundCashTransaction(
        refundAmount: Float,
        startTime: Date
    ) = viewModelScope.launch {

//        if (ENABLE_EMULATOR) {
//            saveTransaction(refundAmount, transaction, storeId, startTime)
//            return@launch
//        }

        printerXManager.validateCashDrawer(onCashDrawerAvailable = {
            try {
                it.openAsync()
                _refundState.emit(RefundState(startTime, refundAmount))
            } catch (e: Exception) {
                _toastMessage.emit(
                    ToastMessage(
                        message = "Failed to open cash drawer",
                        type = ToastMessageType.Error
                    )
                )
                EventUtils.recordException(e)
            }
        }, onCashDrawerNotAvailable = {
            _toastMessage.emit(
                ToastMessage(
                    message = "Cash drawer not available",
                    type = ToastMessageType.Error
                )
            )
        })
    }

    fun onRefundTransaction() = viewModelScope.launch {
        _transaction.value?.let { transaction ->

            if (transaction is SaleTransaction) {
                val refundableAmounts = getRefundableAmounts(transaction)
                val refundToCard =
                    refundableAmounts.cardAmount > 0 || refundableAmounts.ebtAmount > 0

                // Process card refund or EBT refund (In case actual EBT payment was done by checking ebtCardInfo)
                if (refundToCard) {
                    if (storeLevelConfig?.payfac == "cde") {
                        processRefundToSourceV2(transaction)
                    } else {
                        processRefundToSource(transaction, storeLevelConfig)
                    }
                } else {
                    processCashRefund(transaction)
                }
            }
        }
    }

    private fun processCashRefund(transaction: SaleTransaction) {
        val refundableAmounts = getRefundableAmounts(transaction)
        if (refundableAmounts.cashAmount > 0) {
            refundCashTransaction(
                refundAmount = refundableAmounts.cashAmount.toFloat(),
                startTime = Date(),
            )
        }
    }

    private fun saveTransaction(
        refundAmount: Float,
        transaction: SaleTransaction,
        storeId: String,
        startTime: Date
    ) =
        viewModelScope.launch {

            val cashierId = cashierId()

            val fuelAmt = transaction.fuelAmount

            transactionRepository.saveTransaction(
                RefundTransaction(
                    txnEndTime = Date(),
                    txnStartTime = startTime,
                    txnTotalGrandAmount = (transaction.txnTotalGrandAmount - fuelAmt).toFloat()
                        .to2Decimal(),
                    txnTotalGrossAmount = (transaction.txnTotalGrossAmount - fuelAmt).toFloat()
                        .to2Decimal(),
                    txnTotalNetAmount = (transaction.txnTotalNetAmount - fuelAmt).toFloat()
                        .to2Decimal(),
                    txnTotalTaxNetAmount = transaction.txnTotalTaxNetAmount,
                    cashierId = cashierId,
                    txnStatus = TransactionStatus.Complete,
                    statusHistory = hashMapOf(Date().epochInSeconds() to TransactionStatus.Complete),
                    txnItems = transaction.txnItems.filter { it !is TransactionItemWithFuel },
                    txnId = generateTransactionId(
                        storeId,
                        posNumber = _storeConfig.value?.posNumber
                            ?: "1"
                    ),
                    refundInfo = RefundInfo(
                        linkedTxnId = transaction.txnId,
                        reason = "REFUND",
                        epxData = epxData,
                        epxEbtData = epxEbtData
                    ),
                    txnPayment = transaction.txnPayment,
                )
            )
            val transactionToUpdate =
                transaction.copy(txnType = TransactionType.SaleRefund)
            transactionRepository.saveTransaction(transactionToUpdate)
            _transaction.emit(transactionToUpdate)
            _toastMessage.emit(
                ToastMessage(
                    message = "${refundAmount.toDollars()} refunded successfully",
                    type = ToastMessageType.Success
                )
            )
            val printCardReceipt = _storeConfig.value?.receiptInfo?.printCardReceipt
            if (printCardReceipt.isTrue()) {
                reprint()
            }
            epxData = null
            epxEbtData = null
        }

    fun onCashDrawerCloseClick() = viewModelScope.launch {
        printerXManager.validateCashDrawer(onCashDrawerAvailable = {

            val refundState = uiState.value.refundState
            val transaction = _transaction.value

            if (!it.isOpen && refundState != null && transaction is SaleTransaction) {
                saveTransaction(
                    refundState.refundAmount,
                    transaction,
                    _storeConfig.value?.storeCode ?: "",
                    refundState.startTime
                )
                _refundState.emit(null)
            } else {
                _toastMessage.emit(
                    ToastMessage(
                        message = "Please close the cash drawer",
                        type = ToastMessageType.Error
                    )
                )
            }
        }, onCashDrawerNotAvailable = {
            _toastMessage.emit(
                ToastMessage(
                    message = "Cash drawer not available",
                    type = ToastMessageType.Error
                )
            )
        })
    }

    fun onDismissConfirm() = viewModelScope.launch {
        _showConfirm.emit(false)
    }


    fun showConfirmDialog() = viewModelScope.launch {
        _showConfirm.emit(true)
    }

}