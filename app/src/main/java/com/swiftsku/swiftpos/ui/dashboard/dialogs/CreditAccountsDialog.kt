package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.CreditAccountDoc
import com.swiftsku.swiftpos.data.model.CreditStats
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.extension.centsToDollarsString
import com.swiftsku.swiftpos.extension.orNil
import com.swiftsku.swiftpos.ui.components.SearchBar
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.theme.Grey_5F
import com.swiftsku.swiftpos.ui.theme.Grey_D6
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.utils.formatAsUSPhoneNumber
import java.util.Date


@Composable
fun CreditAccountsDialog(
    dashboardVM: DashboardViewModel, creditsVM: CreditsViewModel
) {
    val storeConfig = dashboardVM.storeLevelConfig.collectAsState().value
    val terminalConfig = dashboardVM.storeConfig.collectAsState().value
    val creditAccountsDoc = creditsVM.creditAccountsDoc.collectAsState().value
    val ledgerEntries = creditsVM.ledgerEntries.collectAsState().value
    val creditStats = creditsVM.creditStats.collectAsState().value
    val selectedAccountId = creditsVM.selectedAccountId.collectAsState().value
    var searchQuery by remember { mutableStateOf("") }
    var showAccountForm by remember { mutableStateOf(false) }
    var showFundOptions by remember { mutableStateOf(false) }

    val filteredAccounts = creditAccountsDoc?.creditAccounts.orEmpty().values.filter { account ->
        val name = account.name.lowercase()
        val phone = account.phone.lowercase()
        name.contains(searchQuery) || phone.contains(searchQuery)
    }

    val selectedAccount = creditAccountsDoc?.creditAccounts?.get(selectedAccountId)

    fun handleSaveAccount(
        creditAccountDoc: CreditAccountDoc,
        creditAccount: CreditAccount?,
        name: String,
        phone: String,
        address: String,
        creditLimitCents: Int
    ) {
        val accountId = creditAccount?.id ?: generateTransactionId(
            storeCode = terminalConfig?.storeCode ?: "",
            posNumber = terminalConfig?.posNumber ?: "1"
        )
        val createdAt = creditAccount?.createdAt ?: Date().time
        val duplicate = creditAccountDoc.creditAccounts.values.any { existing ->
            existing.phone == phone && existing.id != creditAccount?.id
        }
        if (duplicate) {
            creditsVM.showToast("Account with this phone already exists", ToastMessageType.Error)
            return
        }
        val updatedAccount = CreditAccount(
            id = accountId,
            name = name,
            phone = phone,
            creditLimitCents = creditLimitCents,
            createdAt = createdAt,
            updatedAt = Date().time,
            address = address,
            currentOutstandingCents = creditAccount?.currentOutstandingCents.orNil()
        )
        val updatedAccounts = creditAccountDoc.creditAccounts.toMutableMap()
        updatedAccounts[accountId] = updatedAccount
        val updatedDoc = creditAccountDoc.copy(creditAccounts = updatedAccounts)
        creditsVM.saveCreditAccount(updatedDoc)
    }

    fun dismissDialog() {
        if (selectedAccount != null) {
            creditsVM.clearSelectedAccount()
        } else {
            dashboardVM.hideCreditAccountsDialog()
        }
    }

    fun handleAccountSelection(account: CreditAccount) {
        creditsVM.setSelectedAccount(account)
    }

    fun handleFundOptionSelection(fundOption: FundOption) {
        showFundOptions = false
        dashboardVM.askForAmount(fundOption)
    }

    val title = selectedAccount?.let { "Credit Account - ${it.name}" } ?: run { "Credit Accounts" }

    Dialog(
        onDismissRequest = { dismissDialog() }, properties = DialogProperties(
            dismissOnBackPress = true, dismissOnClickOutside = true, usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .width(800.dp)
                .heightIn(max = 550.dp)
        ) {
            Column {
                TopAppBar(elevation = 4.dp, title = { Text(title) }, actions = {
                    IconButton(onClick = { dismissDialog() }) {
                        Icon(Icons.Filled.Clear, null)
                    }
                })
                Column(
                    Modifier
                        .background(Grey_5F)
                        .padding(8.dp)
                ) {
                    selectedAccount?.let {
                        AccountDetail(
                            account = it,
                            onAddFundsClick = { showFundOptions = true },
                            onEditClick = { showAccountForm = true },
                            ledgerEntries = ledgerEntries
                        )
                    } ?: run {
                        SummaryCards(creditStats)
                        Spacer(Modifier.height(8.dp))
                        Column(
                            Modifier
                                .background(Color.White, RoundedCornerShape(8.dp))
                                .heightIn(min = 200.dp),
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                SearchBar(query = searchQuery,
                                    onQueryChange = { searchQuery = it },
                                    modifier = Modifier.weight(1f),
                                    searchHint = "Search by account name or phone",
                                    onDebounceQueryChange = { searchQuery = it })
                                Spacer(modifier = Modifier.width(8.dp))
                                Button(
                                    modifier = Modifier.padding(end = 16.dp),
                                    onClick = { showAccountForm = true },
                                    colors = ButtonDefaults.buttonColors(backgroundColor = Teal)
                                ) {
                                    Text("+ New Account")
                                }
                            }
                            AccountsHeader()
                            Divider()
                            if (filteredAccounts.isNotEmpty()) {
                                LazyColumn {
                                    itemsIndexed(
                                        items = filteredAccounts,
                                        key = { _, item -> item.id }) { index, account ->
                                        AccountRow(creditAccount = account,
                                            background = if (index % 2 == 0) Grey_D6 else Color.Transparent,
                                            onClick = { handleAccountSelection(account) })
                                    }
                                }
                            } else {
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "No accounts found.",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.align(Alignment.CenterHorizontally)
                                )
                            }
                            Spacer(modifier = Modifier.height(32.dp))
                        }
                    }
                }
            }

            if (showAccountForm) {
                CreditAccountFormDialog(
                    creditAccount = selectedAccount,
                    onSaveAccount = { creditAccount, name, phone, address, limitInCents ->
                        creditAccountsDoc?.let {
                            handleSaveAccount(
                                creditAccountsDoc, creditAccount, name, phone, address, limitInCents
                            )
                        }
                    },
                    onCancel = { showAccountForm = false },
                    maxCreditLimitCents = storeConfig?.maxCreditLimitCents.orNil()
                )
            }

            if (showFundOptions) {
                AccountFundDialog(::handleFundOptionSelection) { showFundOptions = false }
            }
        }
    }
}

@Composable
fun AccountsHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 8.dp)
    ) {
        Text(
            text = "Account Name", modifier = Modifier.weight(0.25f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Account ID", modifier = Modifier.weight(0.25f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Phone", modifier = Modifier.weight(0.15f), fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Credit Limit",
            modifier = Modifier.weight(0.15f),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "Outstanding",
            modifier = Modifier.weight(0.15f),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End
        )
    }
}

@Composable
fun AccountRow(
    creditAccount: CreditAccount, background: Color, onClick: () -> Unit
) {
    Row(
        Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .background(background)
            .padding(horizontal = 8.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = creditAccount.name, modifier = Modifier.weight(0.25f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(text = creditAccount.id, modifier = Modifier.weight(0.25f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(text = formatAsUSPhoneNumber(creditAccount.phone), modifier = Modifier.weight(0.15f))
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = creditAccount.creditLimitCents.centsToDollarsString(),
            modifier = Modifier.weight(0.15f),
            textAlign = TextAlign.End
        )
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = creditAccount.currentOutstandingCents.centsToDollarsString(),
            modifier = Modifier.weight(0.15f),
            textAlign = TextAlign.End
        )
    }
}

@Composable
fun SummaryCards(creditStats: CreditStats?) {
    Row(verticalAlignment = Alignment.Top) {
        Column {
            if (!creditStats?.topOutstandingAccounts.isNullOrEmpty()) {
                Text(" ", fontSize = 12.sp)
                Spacer(modifier = Modifier.height(4.dp))
            }
            SummaryCard(
                "Total Outstanding",
                creditStats?.totalOutstandingCents.orNil().centsToDollarsString()
            )
        }
        Spacer(modifier = Modifier.width(32.dp))
        if (!creditStats?.topOutstandingAccounts.isNullOrEmpty()) {
            Column {
                Text("Top outstanding accounts", fontSize = 12.sp, maxLines = 1)
                Spacer(modifier = Modifier.height(4.dp))
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    creditStats?.topOutstandingAccounts?.forEach {
                        SummaryCard(it.name, it.currentOutstandingCents.centsToDollarsString())
                    }
                }
            }
        }
    }
}

@Composable
fun SummaryCard(title: String, value: String) {
    Column(
        modifier = Modifier
            .size(width = 160.dp, height = 60.dp)
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(8.dp),
        verticalArrangement = Arrangement.SpaceAround
    ) {
        Text(title, fontSize = 12.sp, maxLines = 1, overflow = TextOverflow.Ellipsis)
        Text(value, fontWeight = FontWeight.Bold, color = Color.Black)
    }
}