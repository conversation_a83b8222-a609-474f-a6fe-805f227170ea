package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel

@Composable
fun FuelPumps(
    fuelPumps: List<FuelPumpState>,
    onPumpClick: (FuelPumpState) -> Unit,
    fdcVM: FDCViewModel
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(6),
    ) {
        items(count = fuelPumps.size, { pump -> pump }) { item ->
            val pumpState = fuelPumps[item]
            FuelCardItem(
                pumpState = pumpState,
                onClick = { onPumpClick(pumpState) },
                fdcVM = fdcVM
            )
        }
    }
}