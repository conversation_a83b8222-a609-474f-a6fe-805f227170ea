package com.swiftsku.swiftpos.ui.dashboard.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.extension.gesturesDisabled
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel


@Composable
fun RowScope.DashboardMain(
    modifier: Modifier = Modifier,
    onCardClick: () -> Unit,
    onEBTClick: () -> Unit,
    onNoSaleClick: () -> Unit,
    onRedeemClick: () -> Unit,
    onSaveClick: () -> Unit,
    onVoidClick: () -> Unit,
    onCashClick: () -> Unit,
    onLoyaltyClick: () -> Unit,
    onCashAmountClick: (amount: Float) -> Unit,
    onPriceBookItemClick: (item: PluItem) -> Unit,
    onPriceCheckClick: (Int) -> Unit,
    onRecallClick: (Int) -> Unit,
    onTxnHistoryClick: (Int) -> Unit,
    totalSale: Float,
    customerCount: Int,
    priceBookItems: List<PluItem>,
    departmentList: List<Department>,
    onDepartmentClick: (item: Department) -> Unit,
    transactionSummary: TransactionSummary,
    onMenuClick: () -> Unit,
    onPayoutClick: () -> Unit,
    onReportClick: (Int) -> Unit,
    onInfoClick: () -> Unit,
    fuelPumps: List<FuelPumpState>,
    onPumpClick: (FuelPumpState) -> Unit,
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel
    ) {
    val menuKeysList by dashboardVM.menuKeys.collectAsState()
    val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()
    val showMenuSection = menuKeysList.any { it.active }
    Column(
        modifier = modifier
            .weight(3.5f, true),
    ) {
        if (storeLevelConfig?.newDashboardUi == false) {
            MainHeader(
                totalSale = totalSale,
                customerCount = customerCount,
                onMenuClick = onMenuClick,
                dashboardVM = dashboardVM
            )
        }
        Column(modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Bottom) {
//            PromotionSection()
            FuelPumps(fuelPumps = fuelPumps, onPumpClick = onPumpClick, fdcVM = fdcVM)
            Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.weight(1f)) {
                DepartmentSection(
                    onDepartmentClick = onDepartmentClick,
                    departmentList = departmentList,
                    modifier = Modifier
                        .weight(5f)
                        .gesturesDisabled(
                            transactionSummary.totalAmountCollected() != 0f
                        ),
                    dashboardVM = dashboardVM
                )
                if (showMenuSection) {
                    MenuKeysSection(
                        modifier = Modifier
                            .weight(3f)
                            .gesturesDisabled(
                                transactionSummary.totalAmountCollected() != 0f
                            ),
                        dashboardVM = dashboardVM
                    )
                }
            }
            Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.weight(1f)) {
                DashboardPayment(
                    modifier = Modifier.weight(3f),
                    onCardClick = onCardClick,
                    onEBTClick = onEBTClick,
                    onNoSaleClick = onNoSaleClick,
                    onRedeemClick = onRedeemClick,
                    onSaveClick = onSaveClick,
                    onVoidClick = onVoidClick,
                    onRefundClick = { dashboardVM.showRefundDialog() },
                    onCashAmountClick = onCashAmountClick,
                    onCashClick = onCashClick,
                    onLoyaltyClick = onLoyaltyClick,
                    transactionSummary = transactionSummary,
                    onPayoutClick = onPayoutClick,
                    dashboardVM = dashboardVM
                )
                DashboardPriceBook(
                    modifier = Modifier
                        .weight(3.5f)
                        .gesturesDisabled(
                            transactionSummary.totalAmountCollected() != 0f
                        ),
                    onPriceBookItemClick = onPriceBookItemClick,
                    priceBookItems = priceBookItems,
                    dashboardVM = dashboardVM

                )
            }
        }
        if (storeLevelConfig?.newDashboardUi.isTrue()) {
            MainFooterNew(
                onPriceCheckClick = onPriceCheckClick,
                onRecallClick = onRecallClick,
                onTxnHistoryClick = onTxnHistoryClick,
                onReportClick = onReportClick,
                onInfoClick = onInfoClick,
                onMenuClick = onMenuClick,
                totalSale = totalSale,
                customerCount = customerCount,
                dashboardVM = dashboardVM

            )
        } else {
            MainFooter(
                onPriceCheckClick = onPriceCheckClick,
                onRecallClick = onRecallClick,
                onTxnHistoryClick = onTxnHistoryClick,
                onReportClick = onReportClick,
                onInfoClick = onInfoClick
            )
        }
    }
}