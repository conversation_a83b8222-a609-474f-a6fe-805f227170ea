package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.model.serializer.DateSerializer
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.StatusReason
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.utils.DocumentFieldKeys
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

@Serializable
data class SaleTransaction(
    override val txnId: String,
    override val txnItems: List<TransactionItem> = emptyList(),
    @Serializable(with = DateSerializer::class)
    override val txnEndTime: Date?,
    val txnTotalGrandAmount: Float = 0f,
    val txnTotalTaxNetAmount: Float = 0f,
    val txnTotalGrossAmount: Float = 0f,
    val txnTotalNetAmount: Float = 0f,
    override val txnType: TransactionType = TransactionType.Sale,
    override val txnStatus: TransactionStatus,
    override val statusHistory: HashMap<Int, TransactionStatus>? = hashMapOf(),
    val dob: String? = null,
    override val cashierId: String,
    val txnPayment: Map<TxnPaymentType, TxnPayment> = emptyMap(),
    @Serializable(with = DateSerializer::class)
    override val txnStartTime: Date = Date(),
    val accountInfo: LoyaltyAccountInfo? = null,
    val txnDiscount: TxnDiscount? = null,
    val lotteryPayout: LotteryPayout? = null,
    val lotteryPayouts: List<LotteryPayout>? = null,
    val cardInfo: CardInfo? = null,
    val fuelMeta: FuelMeta? = null,
    val ebtInfo: EbtInfo? = null,
    val statusReason: StatusReason? = StatusReason.INITIAL,
    @SerialName(DocumentFieldKeys.IS_INTEGRATED_FIELD)
    override val isIntegrated: Boolean = false,
    @SerialName(DocumentFieldKeys.IS_CASH_INTEGRATED_FIELD)
    override val isCashIntegrated: Boolean = false,
    val refundRes: RefundResponse? = null,
    override var paymentRecord: PaymentRecord? = null,
    val appliedFees: List<AppliedFee>? = emptyList(),
    val txnLabel: String = "",
) : Transaction()


fun SaleTransaction.coupon(): Float =
    this.txnDiscount?.coupon?.sumOfFloats { it.amount } ?: 0f

fun SaleTransaction.lottery(): Float = this.lotteryPayouts?.sumOfFloats { it.amount } ?: 0f

fun SaleTransaction.promotion(): Float =
    this.txnItems.sumOfFloats { itemLine ->
        itemLine.promotion?.sumOfFloats { promotion -> promotion.promotionAmount } ?: 0f
    }


val SaleTransaction.fuelAmount
    get(): Double =
        this.txnItems
            .filterIsInstance<TransactionItemWithFuel>()
            .filter { it.status == TransactionItemStatus.Normal }
            .sumOf { it.postFuel?.amount ?: it.preFuel.amount }

val SaleTransaction.fuelVolume
    get() : Double = this.txnItems
        .filterIsInstance<TransactionItemWithFuel>()
        .filter { it.status == TransactionItemStatus.Normal }
        .sumOf { it.postFuel?.volume ?: 0.0 }


val SaleTransaction.showRecall: Boolean get() = txnStatus == TransactionStatus.FuelAuthorized || txnStatus == TransactionStatus.FuelDispensed

val SaleTransaction.showReprint: Boolean get() = txnStatus == TransactionStatus.Complete

fun convertToSaleTransaction(
    transaction: Transaction,
    txnSummary: TransactionSummary,
): SaleTransaction {
    if (transaction is SaleTransaction) return transaction
    return SaleTransaction(
        txnEndTime = transaction.txnEndTime,
        txnStartTime = transaction.txnStartTime,
        txnTotalGrandAmount = txnSummary.transactionTotalGrandAmount,
        txnTotalGrossAmount = txnSummary.transactionTotalGrossAmount,
        txnTotalNetAmount = txnSummary.transactionTotalNetAmount,
        txnTotalTaxNetAmount = txnSummary.transactionTotalTaxNetAmount,
        cashierId = transaction.cashierId,
        txnStatus = transaction.txnStatus,
        txnItems = transaction.txnItems,
        txnId = transaction.txnId,
        statusHistory = transaction.statusHistory
    )
}

fun SaleTransaction.convertToRefundTransaction(
    refundReason: String, linkedTxnId: String
): RefundTransaction {
    return RefundTransaction(
        txnEndTime = txnEndTime,
        txnStartTime = txnStartTime,
        txnTotalGrandAmount = txnTotalGrandAmount,
        txnTotalGrossAmount = txnTotalGrossAmount,
        txnTotalNetAmount = txnTotalNetAmount,
        txnTotalTaxNetAmount = txnTotalTaxNetAmount,
        cashierId = cashierId,
        txnStatus = txnStatus,
        txnItems = txnItems,
        txnId = txnId,
        refundInfo = RefundInfo(linkedTxnId = linkedTxnId, reason = refundReason),
        txnPayment = txnPayment,
        statusHistory = statusHistory
    )
}