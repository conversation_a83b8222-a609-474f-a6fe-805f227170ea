package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.Serializable


@Serializable
data class CreditAccountDoc(
    val docType: String = "accounts_item",
    val storeCode: String,
    val creditAccounts: Map<String, CreditAccount> = emptyMap()
)

@Serializable
data class CreditAccount(
    val id: String,
    val name: String,
    val phone: String,
    val creditLimitCents: Int,
    val createdAt: Long,
    val updatedAt: Long,
    val address: String? = "",
    val currentOutstandingCents: Int
)