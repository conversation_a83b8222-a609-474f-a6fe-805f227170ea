package com.swiftsku.swiftpos.data.model

import com.pax.poslinkadmin.constant.TransactionType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class TxnPayment

@Serializable
data class CashPayment(
    val tender: Float = 0f,
    val change: Float = 0f,
) : TxnPayment()

@Serializable
data class EBTPayment(
    val amount: Float = 0f,
    val paymentType: String = "ebt",
    @SerialName("payfacId")
    val payFacId: String,
    val epxData: EpxData? = null
) : TxnPayment()

@Serializable
data class CardPayment(
    val amount: Float = 0f,
    val paymentType: String = "card",
    @SerialName("payfacId")
    val payFacId: String,
    val brand: String,
    val epxData: EpxData? = null,
    val epxAuthData: EpxData? = null
) : TxnPayment()

@Serializable
data class ChequePayment(
    val amount: Float = 0f,
    val number: String = ""
) : TxnPayment()

@Serializable
data class CreditPayment(
    val amount: Float = 0f,
    val accountId: String = "",
    val accountName: String = "",
    val creditLimitCents: Int,
    val outstandingAmountCents: Int
) : TxnPayment()

val TxnPayment.paymentAmount
    get() = when (this) {
        is CashPayment -> tender - change
        is EBTPayment -> amount
        is CardPayment -> amount
        is ChequePayment -> amount
        is CreditPayment -> amount
    }

@Serializable
data class EpxData(
    val transactionType: TransactionType,
    val amount: Float? = 0f,
    val authorizationCode: String? = null,
    val hostResponseMessage: String? = null,
    val hostResponseNumber: String? = null,
    var errorMessage: String? = null,
    var errorCode: String? = null,
)

fun CashPayment?.mergeChequeIntoCashPayment(chequePayment: ChequePayment?): CashPayment? {
    return if (chequePayment != null) {
        this?.copy(tender = this.tender + chequePayment.amount)
            ?: CashPayment(tender = chequePayment.amount)
    } else {
        this
    }
}

fun CashPayment?.mergeCreditIntoCashPayment(creditPayment: CreditPayment?): CashPayment? {
    return if (creditPayment != null) {
        this?.copy(tender = this.tender + creditPayment.amount)
            ?: CashPayment(tender = creditPayment.amount)
    } else {
        this
    }
}

fun CardPayment?.isEpxCaptured() =
    this?.epxData?.transactionType == TransactionType.POST_AUTHORIZATION
            || this?.epxData?.transactionType == TransactionType.SALE