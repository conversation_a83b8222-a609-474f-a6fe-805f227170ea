package com.swiftsku.swiftpos.data.couchbase.pricebook

import com.swiftsku.swiftpos.data.model.PluItem
import kotlinx.coroutines.flow.Flow

interface PriceBookRepository {
    fun getPriceBookOnDashboardStream(): Flow<List<PluItem>>
    suspend fun deletePriceBook(pluItem: PluItem, storeId: String)
    suspend fun upsertPriceBook(pluItem: PluItem, storeId: String): Boolean
    suspend fun findByBarcode(barcode: String, storeId: String): List<PluItem>
    suspend fun getPriceBooks(storeCode: String, compositePluIds: List<String>): List<PluItem>
    fun getPriceBookStream(docIds: List<String>): Flow<List<PluItem>>
    fun getPriceBookByDeptId(deptId: String): Flow<List<PluItem>>
    suspend fun getPluItem(storeCode: String, pluId: String, pluModifier: String): PluItem?
}