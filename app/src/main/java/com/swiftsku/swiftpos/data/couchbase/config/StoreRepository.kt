package com.swiftsku.swiftpos.data.couchbase.config

import com.swiftsku.swiftpos.data.model.PresetConfig
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.TerminalConfig
import kotlinx.coroutines.flow.Flow

interface StoreRepository {

    suspend fun fetchStoreConfig(serialKey: String): StoreConfig?
    suspend fun storeConfigFlow(serialKey: String): Flow<StoreConfig>
    suspend fun presetConfigFlow(storeCode: String): Flow<PresetConfig?>

    suspend fun fetchTerminalConfig(posId: String): TerminalConfig?

    suspend fun savePluItemsOrder(posId: String, pluOrder: List<String>): Boolean {
        return false
    }

    suspend fun getCurrentBatchId(posId: String): String? {
        return null
    }
    suspend fun updateCurrentBatchId(posId: String, batchId: String): String {
        return ""
    }

    suspend fun updateEpxTerminalIp(posId: String, ip: String): Boolean {
        return false
    }
}