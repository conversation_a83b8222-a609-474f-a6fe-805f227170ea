package com.swiftsku.swiftpos.data.couchbase.credit

import com.couchbase.lite.Collection
import com.couchbase.lite.DataSource
import com.couchbase.lite.Expression
import com.couchbase.lite.MutableDocument
import com.couchbase.lite.Ordering
import com.couchbase.lite.QueryBuilder
import com.couchbase.lite.SelectResult
import com.couchbase.lite.UnitOfWork
import com.couchbase.lite.queryChangeFlow
import com.swiftsku.swiftpos.data.couchbase.Couchbase
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.model.AccountLedgerTxn
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.CreditAccountDoc
import com.swiftsku.swiftpos.data.model.LedgerEntry
import com.swiftsku.swiftpos.data.model.LedgerEntryData
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.di.qualifiers.AccountLedgersCollection
import com.swiftsku.swiftpos.di.qualifiers.CreditAccountsCollection
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.TransactionCollection
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.utils.EventUtils
import java.util.Date
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class CreditRepository
@Inject
constructor(
        private val couchbaseDb: Couchbase,
        @CreditAccountsCollection private val accountsCollection: Collection,
        @AccountLedgersCollection private val ledgersCollection: Collection,
        @TransactionCollection private val txnCollection: Collection,
        private val transactionRepository: TransactionRepository,
        @IODispatcher private val dispatcher: CoroutineDispatcher,
        private val json: Json
) {

    fun getCreditAccount(storeCode: String): Flow<CreditAccountDoc?> {
        return callbackFlow {
            val doc = accountsCollection.getDocument(storeCode)
            doc?.toJSON()?.let {
                val creditAccountDoc = json.decodeFromString<CreditAccountDoc>(it)
                trySend(creditAccountDoc)
            }

            // Add document change listener and send updates through
            val listener =
                    accountsCollection.addDocumentChangeListener(storeCode) { _ ->
                        val updatedDoc = accountsCollection.getDocument(storeCode)
                        updatedDoc?.toJSON()?.let {
                            val creditAccountDoc = json.decodeFromString<CreditAccountDoc>(it)
                            trySend(creditAccountDoc)
                        }
                    }

            awaitClose { listener.remove() }
        }
                .flowOn(dispatcher)
                .catch { e -> EventUtils.recordException(e) }
    }

    suspend fun saveCreditAccounts(creditAccountDoc: CreditAccountDoc): Boolean {
        return withContext(dispatcher) {
            try {
                val encodeToString = json.encodeToString(creditAccountDoc)
                val document = MutableDocument(creditAccountDoc.storeCode, encodeToString)
                accountsCollection.save(document)
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    fun getLedgerEntries(accountId: String): Flow<List<LedgerEntry>> {
        val query =
                QueryBuilder.select(SelectResult.all())
                        .from(DataSource.collection(ledgersCollection))
                        .where(
                                Expression.property("accountId")
                                        .equalTo(Expression.string(accountId))
                        )
                        .orderBy(Ordering.property("txnTime").descending())

        return query.queryChangeFlow()
                .flowOn(dispatcher)
                .map { queryChange ->
                    val ledgerEntries = mutableListOf<LedgerEntry>()
                    queryChange.results?.let { results ->
                        results.forEach { result ->
                            try {
                                result?.toJSON()?.let { jsonString ->
                                    json.decodeFromString<Map<String, LedgerEntry>>(jsonString)[
                                                    "accountLedgers"]
                                            ?.let { ledgerEntries.add(it) }
                                }
                            } catch (e: Exception) {
                                EventUtils.recordException(e)
                            }
                        }
                    }
                    ledgerEntries.distinctBy { it.txnId }
                }
                .catch { EventUtils.recordException(it) }
    }

    suspend fun createLedgerEntry(
            creditAccount: CreditAccount,
            creditAccountDoc: CreditAccountDoc,
            ledgerEntry: LedgerEntry
    ): Boolean {
        return withContext(dispatcher) {
            try {
                // Update current outstanding amount
                val creditAccountDocString = json.encodeToString(creditAccountDoc)
                val accountDoc = MutableDocument(creditAccountDoc.storeCode, creditAccountDocString)
                accountsCollection.save(accountDoc)

                // Create ledger entry
                val ledgerEntryString = json.encodeToString(ledgerEntry)
                val ledgerDoc = MutableDocument(ledgerEntry.txnId, ledgerEntryString)
                ledgersCollection.save(ledgerDoc)

                // Create AccountLedgerTxn
                val accountLedgerTxn =
                        AccountLedgerTxn(
                                txnId = ledgerEntry.txnId,
                                txnStartTime = Date(),
                                statusHistory =
                                        hashMapOf(
                                                Date().epochInSeconds() to
                                                        TransactionStatus.Complete
                                        ),
                                cashierId = ledgerEntry.cashierId,
                                data =
                                        LedgerEntryData(
                                                creditAccount.id,
                                                creditAccount.name,
                                                ledgerEntry.amountCents,
                                                ledgerEntry.ledgerReason,
                                                ledgerEntry.fundMop,
                                                creditAccount.currentOutstandingCents,
                                                ledgerEntry.notes
                                        )
                        )
                transactionRepository.saveTransaction(accountLedgerTxn)
                //                val accountLedgerTxnString = json.encodeToString(accountLedgerTxn)
                //                val accountLedgerTxnDoc =
                //                    MutableDocument(accountLedgerTxn.txnId,
                // accountLedgerTxnString)
                //                accountLedgerTxnDoc.setString("doc_type", "txn_item")
                //                accountLedgerTxnDoc.setBoolean("is_integrated", false)
                //                accountLedgerTxnDoc.setString("store_code", ledgerEntry.storeCode)

                val existingDoc = txnCollection.getDocument(accountLedgerTxn.txnId)
                val encodeToString = json.encodeToString(accountLedgerTxn)
                val document =
                        MutableDocument(
                                accountLedgerTxn.txnId,
                                encodeToString,
                        )
                //                document.setString("doc_type", "txn_item")
                //                document.setBoolean("is_integrated", false)
                //                document.setString("store_code", accountLedgerTxn.txnId.take(5))
                //                txnCollection.save(document)

                //                if (existingDoc == null) {
                //
                //                } else {
                //                    val newDocument = MutableDocument(
                //                        transaction.txnId,
                //                        encodeToString,
                //                    )
                //                    val newDocMap = newDocument.toMap().toMutableMap()
                //                    val newDoc = existingDoc.toMutable().setData(newDocMap)
                //                    newDoc.setString("doc_type", "txn_item")
                //                    newDoc.setBoolean("is_integrated", false)
                //                    newDoc.setString("store_code", transaction.txnId.take(5))
                //                    collection.save(newDoc)
                //                }

                couchbaseDb
                        .getDb()
                        .inBatch(
                                UnitOfWork {
                                    accountsCollection.save(accountDoc)
                                    ledgersCollection.save(ledgerDoc)
                                    //                    txnCollection.save(accountLedgerTxnDoc)
                                }
                        )
                return@withContext true
            } catch (e: Exception) {
                EventUtils.recordException(e)
                return@withContext false
            }
        }
    }

    fun createCollection() {
        val creditAccDoc = CreditAccountDoc(storeCode = "1ff6e")
        val creditAccountDocString = json.encodeToString(creditAccDoc)
        val accountDoc = MutableDocument(creditAccDoc.storeCode, creditAccountDocString)
        accountsCollection.save(accountDoc)
    }
}
