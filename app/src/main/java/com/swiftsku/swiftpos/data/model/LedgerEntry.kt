package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.TxnPaymentType
import kotlinx.serialization.Serializable


@Serializable
data class LedgerEntry(
    val txnId: String,
    val accountId: String,
    val linkedTxnId: String,
    val txnTime: Long,
    val cashierId: String,
    val ledgerReason: LedgerReason,
    val ledgerDirection: LedgerDirection,
    val fundMop: TxnPaymentType? = null,
    val notes: String? = null,
    val amountCents: Int,
    val newAccountOutstandingCents: Int,
    val docType: String = "ledger_item",
    val storeCode: String,
)

enum class LedgerDirection {
    CREDIT, // Money into account (e.g., Add Funds)
    DEBIT   // Money out of account (e.g., Sale)
}

enum class LedgerReason(val value: String) {
    ADD_FUNDS("Add funds"),
    SALE("Credit Sale"),
    ADJUSTMENT("Adjustment")
}
