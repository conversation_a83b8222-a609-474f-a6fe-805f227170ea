package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.TxnPaymentType
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder


@Serializable
data class LedgerEntry(
    val txnId: String,
    val accountId: String,
    val linkedTxnId: String,
    val txnTime: Long,
    val cashierId: String,
    val ledgerReason: LedgerReason,
    val ledgerDirection: LedgerDirection,
    val fundMop: TxnPaymentType? = null,
    val notes: String? = null,
    val amountCents: Int,
    val newAccountOutstandingCents: Int,
    val docType: String = "ledger_item",
    val storeCode: String,
)

enum class LedgerDirection {
    CREDIT, // Money into account (e.g., Add Funds)
    DEBIT   // Money out of account (e.g., Sale)
}

@Serializable(with = LedgerReasonSerializer::class)
enum class LedgerReason(val value: String) {
    ADD_FUNDS("Add funds"),
    SALE("Credit Sale"),
    ADJUSTMENT("Adjustment")
}

object LedgerReasonSerializer : KSerializer<LedgerReason> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("LedgerReason", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: LedgerReason) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): LedgerReason {
        return when (val value = decoder.decodeString()) {
            "Add funds" -> LedgerReason.ADD_FUNDS
            "Credit Sale" -> LedgerReason.SALE
            "Adjustment" -> LedgerReason.ADJUSTMENT
            else -> throw IllegalArgumentException("Unknown LedgerReason: $value")
        }
    }
}
