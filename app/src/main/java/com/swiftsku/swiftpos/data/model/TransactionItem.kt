package com.swiftsku.swiftpos.data.model

import com.swiftsku.swiftpos.data.type.TransactionItemLineType
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.extension.isTrue
import kotlinx.serialization.Serializable
import java.util.UUID

@Serializable
sealed class TransactionItem {
    abstract val transactionItemId: String
    abstract val lineType: TransactionItemLineType
    abstract val lineTaxAmount: Float
    abstract val quantity: Int
    abstract var status: TransactionItemStatus
    abstract val promotion: List<Promotion>?
    abstract val discount: TxnItemDiscounts
    abstract val taxation: Map<String, Float>
    abstract val paid: Boolean?
}

val TransactionItem.disableGesture: Boolean get() = paid == true

fun TransactionItem.itemName(): String = when (this) {
    is TransactionItemWithPLUItem -> pluItem.description
    is TransactionItemWithDepartment -> department.departmentName
    is TransactionItemWithFuel -> fuelName()
}

fun TransactionItemWithFuel.fuelName(): String {
    val info = StringBuilder()
    return postFuel?.let {
        it.product1?.let { info.append(it) }
        it.product2?.let { info.append(" + $it") }
        info.append(" (${postFuel.volume}gal)")
        info.toString()
    } ?: run {
        "Pump No${preFuel.pumpNo}"
    }
}

fun TransactionItem.isEBT(): Boolean {
    if (this is TransactionItemWithPLUItem) {
        return this.pluItem.isEBT()
    } else if (this is TransactionItemWithDepartment) {
        return department.isEBT()
    }
    return false
}

fun TransactionItem.isPriceBookLine(): Boolean = this is TransactionItemWithPLUItem

fun TransactionItem.unitPrice(): Float = when (this) {
    is TransactionItemWithPLUItem -> pluItem.price
    is TransactionItemWithDepartment -> department.departmentPrice
    is TransactionItemWithFuel -> postFuel?.amount?.toFloat() ?: preFuel.amount.toFloat()
}

fun TransactionItem.isEbtSupported(department: Department?): Boolean {
    if (this is TransactionItemWithPLUItem) {
        return department?.ebtSupported ?: true
    } else if (this is TransactionItemWithDepartment) {
        return this.department.ebtSupported.isTrue()
    }
    return false
}

fun TransactionItem.totalItemPrice(): Float = when (this) {
    is TransactionItemWithPLUItem -> pluItem.price * quantity
    is TransactionItemWithDepartment -> department.departmentPrice * quantity
    is TransactionItemWithFuel -> postFuel?.amount?.toFloat() ?: preFuel.amount.toFloat()
}

fun TransactionItem.getTaxIds(): List<String> {
    return when (this) {
        is TransactionItemWithPLUItem -> pluItem.taxIds
        is TransactionItemWithDepartment -> department.taxes
        is TransactionItemWithFuel -> emptyList()
    }
}

@Serializable
data class TransactionItemWithPLUItem(
    override val transactionItemId: String = UUID.randomUUID().toString(),
    val pluItem: PluItem,
    override val lineType: TransactionItemLineType = TransactionItemLineType.Item,
    override val lineTaxAmount: Float = 0f,
    override val quantity: Int = 1,
    override var status: TransactionItemStatus = TransactionItemStatus.Normal,
    override var promotion: List<Promotion>? = null,
    override val discount: TxnItemDiscounts = TxnItemDiscounts(),
    override val taxation: Map<String, Float> = emptyMap(),
    override val paid: Boolean? = null
) : TransactionItem()

@Serializable
data class TransactionItemWithDepartment(
    override val transactionItemId: String = UUID.randomUUID().toString(),
    val department: Department,
    override val lineType: TransactionItemLineType = TransactionItemLineType.Merchandise,
    override val lineTaxAmount: Float = 0f,
    override val quantity: Int = 1,
    override var status: TransactionItemStatus = TransactionItemStatus.Normal,
    override var promotion: List<Promotion>? = null,
    override val discount: TxnItemDiscounts = TxnItemDiscounts(),
    override val taxation: Map<String, Float> = emptyMap(),
    override val paid: Boolean? = null
) : TransactionItem()

@Serializable
data class TransactionItemWithFuel(
    override val transactionItemId: String = UUID.randomUUID().toString(),
    val preFuel: PreFuel,
    val postFuel: PostFuel? = null,
    override val lineType: TransactionItemLineType = TransactionItemLineType.Fuel,
    override val lineTaxAmount: Float = 0f,
    override val quantity: Int = 1,
    override var status: TransactionItemStatus = TransactionItemStatus.Normal,
    override val promotion: List<Promotion>? = null,
    override val discount: TxnItemDiscounts = TxnItemDiscounts(),
    override val taxation: Map<String, Float> = emptyMap(),
    override val paid: Boolean? = null
) : TransactionItem()



