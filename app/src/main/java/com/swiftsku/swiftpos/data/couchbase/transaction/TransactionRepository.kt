package com.swiftsku.swiftpos.data.couchbase.transaction

import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalTime

interface TransactionRepository {

    suspend fun saveTransaction(transaction: Transaction): Boolean // return true if success, false otherwise
    suspend fun completeRefundTransaction(refundTransaction: RefundTransaction): Boolean
    suspend fun deleteTransaction(transaction: Transaction): Boolean // return true if success, false otherwise
    suspend fun getTransaction(transactionID: String): Transaction?

    suspend fun getFuelTransactions(deviceId: Int): List<SaleTransaction>
    suspend fun getPendingFuelTransactions(): List<SaleTransaction>
    suspend fun getPendingFuelTransactionsFlow(): Flow<List<SaleTransaction>>
    suspend fun getTransactions(
        transactionStatus: TransactionStatus? = null,
        txnType: TransactionType? = null,
        searchQuery: String? = null,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        startTime: LocalTime? = null,
        endTime: LocalTime? = null,
        limit: Int = 50,
        offset: Int = 0
    ): List<Transaction>

    fun getTransactionsFlow(
        transactionStatus: TransactionStatus? = null,
        txnType: TransactionType? = null,
        searchQuery: String? = null,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        startTime: LocalTime? = null,
        endTime: LocalTime? = null,
        limit: Int = 50,
        offset: Int = 0
    ): Flow<List<Transaction>>

    fun getCustomerCountAndCurrentSales(epoch: Long): Flow<Pair<Int, Float>> // return the number of customers


}