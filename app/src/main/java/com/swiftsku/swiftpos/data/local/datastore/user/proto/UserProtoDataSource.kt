package com.swiftsku.swiftpos.data.local.datastore.user.proto

import androidx.datastore.core.DataStore
import com.swiftsku.swiftpos.data.local.datastore.user.User.UserProto
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import com.swiftsku.swiftpos.data.local.datastore.user.dto.toDTO
import com.swiftsku.swiftpos.data.local.datastore.user.dto.toProto
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.utils.EventUtils
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import javax.inject.Inject


class UserProtoDataSource @Inject constructor(
    @IODispatcher private val dispatcher: CoroutineDispatcher,
    private val dataStore: DataStore<UserProto>
) : UserDataSource {
    override suspend fun saveUser(user: UserDTO): Boolean = withContext(dispatcher) {
        try {
            dataStore.updateData { user.toProto() }
            true
        } catch (e: Exception) {
            EventUtils.recordException(e)
            false
        }
    }

    override suspend fun getCurrentUser(): UserDTO? = withContext(dispatcher) {
        try {
            dataStore.data.first().toDTO()
        } catch (e: Exception) {
            EventUtils.recordException(e)
            null
        }
    }

    override suspend fun clearUser(): Boolean = withContext(dispatcher) {
        try {
            dataStore.updateData {
                it.toBuilder().clear().build()
            }
            true
        } catch (e: Exception) {
            EventUtils.recordException(e)
            false
        }
    }

    override suspend fun isUserLoggedIn(): Boolean = withContext(dispatcher) {
        try {
            dataStore.data.first().loggedIn
        } catch (e: Exception) {
            EventUtils.recordException(e)
            false
        }
    }
}