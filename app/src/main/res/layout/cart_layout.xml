<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/header"
        layout="@layout/cart_list_item"
        app:layout_constraintBottom_toTopOf="@+id/rvCart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCart"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:textAlignment="center"
        app:layout_constraintBottom_toTopOf="@+id/cartSummary"
        app:layout_constraintEnd_toEndOf="@+id/header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header"
        tools:listitem="@layout/cart_list_item" />

    <LinearLayout
        android:id="@+id/cartSummary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvCart">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <TextView
                android:id="@+id/tvSubTotalTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Sub total" />

            <TextView
                android:id="@+id/tvSubTotalValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tvTaxTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"

                android:layout_weight="1"
                android:text="Tax" />

            <TextView
                android:id="@+id/tvTaxValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"

                android:gravity="end"
                android:text="$0.00" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCoupon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvCouponTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Coupon"
                android:textColor="@android:color/holo_green_dark" />

            <TextView
                android:id="@+id/tvCouponValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@android:color/holo_green_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llLottery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"

            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvLotteryTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Lottery"
                android:textColor="@android:color/holo_green_dark" />

            <TextView
                android:id="@+id/tvLotteryValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@android:color/holo_green_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPromotion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvPromotionTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Promotion"
                android:textColor="@android:color/holo_green_dark" />

            <TextView
                android:id="@+id/tvPromotionValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@android:color/holo_green_dark" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tvTotalTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"

                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Grand Total"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tvTotalValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"

                android:gravity="end"
                android:text="$0.00"
                android:textColor="@color/black" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llAmountCollected"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvAmountCollectedTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"

                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Amount Collected"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tvAmountCollectedValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@color/black" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCardFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:visibility="gone">
            <TextView
                android:id="@+id/tvCardFeeTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Card Fee"
                android:textColor="@color/black" />
            <TextView
                android:id="@+id/tvCardFeeValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@color/black" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCardTotal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:visibility="gone">
            <TextView
                android:id="@+id/tvCardTotalTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Card Total"
                android:textColor="@color/black" />
            <TextView
                android:id="@+id/tvCardTotalValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="$0.00"
                android:textColor="@color/black" />
        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>