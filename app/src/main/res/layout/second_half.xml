<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvAvailableOffers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:text="Available offers on the cart"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvOffers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toTopOf="@id/clFooter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableOffers"
        tools:listitem="@layout/offer_list_item" />

    <TextView
        android:id="@+id/tvNoOffers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="32dp"
        android:layout_weight="1"
        android:text="No offers"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableOffers" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFooter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="4dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/llCash"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:background="@color/blue"
            android:orientation="vertical"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/llCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvCashLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="CASH"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvCashAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="$11.11" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCard"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:background="@color/blue"
            android:orientation="vertical"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivLogo"
            app:layout_constraintStart_toEndOf="@id/llCash"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvCardLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="CARD"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvCardAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="$11.42" />
        </LinearLayout>

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_marginStart="4dp"
            android:padding="8dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_app_logo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/llCard"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/phonePad"
        layout="@layout/phone_numpad"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/clFooter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>