<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:baselineAligned="false">


    <include
        android:id="@+id/cartLayout"
        layout="@layout/cart_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvThankYou"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <include
        android:id="@+id/secondHalf"
        layout="@layout/second_half"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/cartLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</LinearLayout>